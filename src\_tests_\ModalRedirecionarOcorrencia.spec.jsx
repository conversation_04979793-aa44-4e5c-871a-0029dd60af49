import React from 'react';
import {render, screen, waitFor, fireEvent} from "@testing-library/react";
import ModalRedirecionarOcorrencia from '../components/ModalRedirecionarOcorrencia';

describe("Testing Ocorrencias", () => {


    it("Modal de Viatura", () => {
        const screen = render(<ModalRedirecionarOcorrencia
            row={{
                "id": 55,
                "id_prop": 5,
                "id_fzd": 11,
                "id_motivo": 1,
                "descricao": "Hrchuy hrfhhur ",
                "id_viat": null,
                "registro_delegacia": false,
                "observacao": null,
                "status": "Aguardando atendimento",
                "created_at": "2021-12-21T21:14:15.000Z",
                "updated_at": "2021-12-21T21:14:15.000Z"
            }}/>);

    
        expect(screen).toBeTruthy();
    })

    it("Modal de Viatura + render de Unidade e Area de Atuação", () => {
        const screen = render(<ModalRedirecionarOcorrencia
            row={{
                "id": 55,
                "id_prop": 5,
                "id_fzd": 11,
                "id_motivo": 1,
                "descricao": "Hrchuy hrfhhur ",
                "id_viat": null,
                "registro_delegacia": false,
                "observacao": null,
                "status": "Aguardando atendimento",
                "created_at": "2021-12-21T21:14:15.000Z",
                "updated_at": "2021-12-21T21:14:15.000Z"
            }}/>);
    
        const unidade = screen.findByText('Unidade');

        const area = screen.findByText("Area de Atuação")
    
        expect(unidade).toBeTruthy();
        expect(area).toBeTruthy();
    })

    it("Modal de Viatura + render de Unidade CIPE CERRADO aparecendo na tela", () => {
        const screen = render(<ModalRedirecionarOcorrencia
            row={{
                "id": 55,
                "id_prop": 5,
                "id_fzd": 11,
                "id_motivo": 1,
                "descricao": "Hrchuy hrfhhur ",
                "id_viat": null,
                "registro_delegacia": false,
                "observacao": null,
                "status": "Aguardando atendimento",
                "created_at": "2021-12-21T21:14:15.000Z",
                "updated_at": "2021-12-21T21:14:15.000Z"
            }}/>);
    
        const unidade = screen.findByText('CIPE CERRADO');
    
        expect(unidade).toBeTruthy();
    })
    
})
