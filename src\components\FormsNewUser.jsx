import React, {useContext, useEffect, useState} from 'react';
import { useUserContext } from '../context/authContext';
import { AiOutlineCloudUpload } from "react-icons/ai"

import axios from 'axios';

import { DropzoneDialog } from 'material-ui-dropzone';

import { 
    Container,
    Row, 
    Col, 
    Card, 
    Form, 
    Button, 
    InputGroup,
    FormControl,
    Spinner
} from 'react-bootstrap'
import { URL } from '../services';
import Swal from 'sweetalert2';
import InputMask from 'react-input-mask'

function FormsNewUser({closeModal}) {
    const {toggle,settoggle} = useUserContext();

    const [open, setOpen] = useState(false);
    const [load, setLoad] =useState(false);

    const [nome, setNome] = useState();
    const [email, setEmail] = useState();
    const [unidade, setUnidade] = useState('CPRO');
    const [status, setStatus] = useState('ATIVO');
    const [foto, setFoto] = useState();
    const [telefone, setTelefone] = useState();
    const [coord,setCoord] = useState(false);
    const [posto, setPosto] = useState('CEL-PM');
    const [senha, setSenha] = useState();
    const [matricula, setMatricula] = useState();
    const [id, setId] = useState();
    const { setTextHeader } = useUserContext();

    setTextHeader('Cadastrar Usuários')
  
    function gerarPassword() {
        setSenha(Math.random().toString(36).slice(-10));
    }

    function submit(event){
        setLoad(true);
        event.preventDefault();
        const tel = telefone.replace('(','')
        const tel2 = tel.replace(')','')
        const tel3 = tel2.replace(' ','')
        axios.post(URL+'salvar_pm',{
            posto_graduacao: posto,
            nome: nome,
            foto: foto ? foto.name : "",
            unidade: unidade,
            email: email,
            telefone: telefone,
            coordenador: coord,
            ativo_inativo: status,
            senha: senha,
            matricula: matricula
        })
        .then(res=>{
            if(res.data.errmessage){
                setLoad(false)
                Swal.fire(res.data.errmessage)
                return 
            }
            
            axios.post(URL+'user_insert',{
                ID_FAZENDA: null,
                NOME: nome,
                USUARIO: matricula,
                SENHA: senha,
                EMAIL: email,
                TELEFONE: '+55'+tel3, 
                STATUS: 1,
                PERMISSAO: 0,
                IDENTIFICACAO: coord? 'COORDPM' : 'PM'
            })
            .then(res=>{
                setLoad(false);
                Swal.fire(nome,' cadastrado com sucesso!','success')
                closeModal()
                settoggle(!toggle)
            })
            .catch(err=>{
                console.log(err)
            })
                
        })
        .catch(res =>{
            setLoad(false)
            Swal.fire('Erro','Tente novamente!',)
        })
    }
  
  return (
    <>

        <Form onSubmit={submit}>
            <Row className="g-2">
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicEmail">
                        <Form.Label className='customLabel'>Posto/Graduação <font className='obr'>*</font></Form.Label>
                        <select className='customInput shadow form-control' value={posto} onChange={e => setPosto(e.target.value)}>
                            <option selected value='CEL-PM'>CEL-PM</option>
                            <option value='TC-PM'>TC-PM</option>    
                            <option value='MAJ-PM'>MAJ-PM</option>    
                            <option value='CAP-PM'>CAP-PM</option>    
                            <option value='TEN-PM'>TEN-PM</option>    
                            <option value='ASP-PM'>ASP-PM</option>    
                            <option value='SUB-PM'>SUB-PM</option>    
                            <option value='SGT-PM'>SGT-PM</option>    
                            <option value='CB-PM'>CB-PM</option>    
                            <option value='SD-PM'>SD-PM</option>    
                        </select>
                    </Form.Group>
                </Col>
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Nome <font className='obr'>*</font></Form.Label>
                        <Form.Control  className='customInput shadow' type="name" placeholder="Digite aqui" onChange={e => setNome(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicEmail">
                        <Form.Label className='customLabel'>Unidade <font className='obr'>*</font></Form.Label>
                        <select className='customInput shadow form-control' value={unidade} onChange={e=>setUnidade(e.target.value)}>
                            <option selected value='CPRO'>CPRO</option>
                            <option value='CIPT-O'>CIPT-O</option>    
                            <option value='85a. CIPM'>85a. CIPM</option>    
                            <option value='86a. CIPM'>86a. CIPM</option>    
                            <option value='CIPRv'>CIPRv</option>    
                            <option value='CIPE CERRADO'>CIPE CERRADO</option>     
                        </select>
                    </Form.Group>
                </Col>
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Email <font className='obr'>*</font></Form.Label>
                        <Form.Control  className='customInput shadow' type="email" placeholder="Digite aqui" onChange={e=>setEmail(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicEmail">
                        <Form.Label className='customLabel'>Status <font className='obr'>*</font></Form.Label>
                        <select  className='customInput shadow form-control' value={status} onChange={e=>setStatus(e.target.value)}>
                            <option selected value="ATIVO">ATIVO</option>
                            <option value="INATIVO">INATIVO</option>       
                        </select>
                    </Form.Group>
                </Col>
                <Col md>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Telefone <font className='obr'>*</font></Form.Label>
                        <InputMask style={{padding:'12px', width:'220px', outline:'0'}} className='customInput shadow' mask="(99) 999999999" type="text" placeholder="Digite aqui" onChange={e=>setTelefone(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
            <Col md>
                    <Form.Label className='customLabel'>Foto <font className='obr'>*</font></Form.Label>
                    <br/>
                    <Button variant='outline-secondary' className='customInput shadow uploadFoto' style={{width:220}} onClick={() => setOpen(true)}>{foto ? foto.name : <div>Faça o Upload <AiOutlineCloudUpload style={{fontSize:25}}/></div>}</Button>
                    <DropzoneDialog
                        acceptedFiles={['image/*']}
                        cancelButtonText={"cancel"}
                        submitButtonText={"submit"}
                        open={open}
                        onClose={() => setOpen(false)}
                        onSave={(files) => {
                            setFoto(files[0])
                            setOpen(false);
                        }}
                        showPreviews={true}
                        showFileNamesInPreview={true}
                        dropzoneText={"Clique, ou arraste e solte a imagem aqui"}
                    />
                </Col>
                <Col>
                    <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                        <Form.Label className='customLabel'>Matricula <font className='obr'>*</font></Form.Label>
                        <Form.Control  className='customInput shadow' type="name" placeholder="Digite aqui" onChange={e => setMatricula(e.target.value)} required/>
                    </Form.Group>
                </Col>
            </Row>
            <Row className="g-2">
                <Col md>
                    <Form.Label className='customLabel'>Senha <font className='obr'>*</font></Form.Label>
                    <InputGroup className="mb-3"
                        className='customInput shadow-sm'>
                        <FormControl
                        placeholder="Senha"
                        aria-label="Senha"
                        aria-describedby="basic-addon2"
                        value={senha}
                        style={{backgroundColor:'#fff'}}
                        className='customInput shadow'
                        readOnly
                        />
                        <Button variant="outline-secondary" id="button-addon2" className='customInput shadow-sm'onClick={gerarPassword}>
                            Gerar Senha
                        </Button>
                    </InputGroup>
                </Col>
            </Row>
            <Form.Group className="mb-3" controlId="formBasicCheckbox">
                <Form.Check 
                    type="checkbox" 
                    label="Coordenador"
                    defaultChecked={coord}
                    onChange={() => setCoord(!coord)}
                    className='mt-2'
                />
            </Form.Group>
            <div class="col text-center mt-3">
                <Button className='blueColor btnCadastro' type="submit" size='lg'>
                    {load 
                        ? 
                        <Spinner animation="border" />
                        :
                        'Cadastrar Usuário'
                    }
                </Button>
            </div>
        </Form>
    </>
  );
}

export default FormsNewUser;