import React, { useState,useEffect } from 'react'
import { URL } from '../../../../services/index'
import PanicSingle from './PanicSingle';
import { TableCell,TableHead, TableRow, TableBody } from '@mui/material';
import { TableComponent } from '../../../components/organisms';

function PanicList() {
    const [panics, setPanics] = useState([])
    const [fazendas, setFazendas] = useState([])
    const [areas, setAreas] = useState([])
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    
    useEffect(() => {
        async function getData(){
            console.log(process.env.REACT_APP_TESTE);
            let data = await fetch(`${URL}todos_panicos`);
            data = await data.json();
            setPanics(data)
        }

        async function getFazendas() {
            let data = await fetch(`${URL}todas_fazendas`)
            data = await data.json()
            setFazendas(data)
        }

        async function getAreas(){
            let data = await fetch(`${URL}todas_areas`)
            data = await data.json()
            setAreas(data)
        }
        
        getData()
        getFazendas()
        getAreas()

    }, [])

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
      };
    
      const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
      };
      
    return (
        <>
            {panics
                ? 
                <TableComponent 
                    count={panics.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Acionamentos Registrados'
                >
                        <TableHead>
                            <TableRow>
                                <TableCell>Data</TableCell>
                                <TableCell>Fazenda/Proprietário</TableCell>
                                <TableCell>Área de atuação</TableCell>
                                <TableCell>Viatura</TableCell>
                                <TableCell>Status</TableCell>
                                <TableCell></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            <PanicSingle panicos={panics} fazendas={fazendas} areas={areas} page={page} rowsPerPage={rowsPerPage}/>
                        </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>
    )
}

export default PanicList
