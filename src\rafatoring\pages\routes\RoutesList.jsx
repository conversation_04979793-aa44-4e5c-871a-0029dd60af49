import React, {useState,useEffect} from 'react'
import RoutesSingle from './RoutesSingle';
import { TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { TableComponent } from '../../components/organisms';

function RoutesList({routes, getRoutes, areaSelected}) {
    
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    
    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };
    
    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    return (
        <>
            {routes
                ?
                <TableComponent
                    count={routes.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Listagem de Rotas'
                >
                    <TableHead>
                        <TableRow>
                            <TableCell>Data inicio</TableCell>
                            <TableCell align='center'>Fazendas</TableCell>
                            <TableCell align='center'>Área de Atuação</TableCell>
                            <TableCell align='center'>Viatura</TableCell>
                            <TableCell align='center'>Status</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {routes.length == 0
                            ?
                            <p>Sem rotas para essa área</p>
                            :
                            <RoutesSingle data={routes} getRoutes={getRoutes} page={page} rowsPerPage={rowsPerPage}/>
                        }
                    </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>
    )
}

export default RoutesList
