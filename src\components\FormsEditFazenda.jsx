import React, {useContext, useEffect, useState} from 'react';
import Header from '../components/Header'
import Footer from '../components/Footer'

import axios from 'axios';

import { 
    Container,
    Row, 
    Col, 
    Card, 
    Form, 
    Button, 
    InputGroup,
    FormControl,
    Spinner,
    Modal
} from 'react-bootstrap'
import { URL } from '../services';
import Swal from 'sweetalert2';
import { Typeahead } from 'react-bootstrap-typeahead'; // ES2015
import { useImmer } from 'use-immer';
import { IoClose } from "react-icons/io5"

function FormsEditFazenda({open, onHide, fazenda, refresh}) {

    const [load, setLoad] = useState(false);
    const [nome, setNome] = useState(fazenda.nome_da_fazenda);
    const [prop, setProp] = useState(fazenda.proprietario);
    const [municipio, setMunicipio] = useState(fazenda.municipio);
    const [codigo, setCodigo] = useState(fazenda.codigo_da_fazenda);
    const [latitude, setLatitude] = useState(fazenda.latitude);
    const [longitude,setLongitude] = useState(fazenda.longitude);
    const [area, setArea] = useState(fazenda.id_area && fazenda.area_de_operacao ? {
        id: fazenda.id_area,
        name: fazenda.area_de_operacao
    } : null );
    const [options, setOptions] = useImmer([]);
    const [status, setStatus] = useState(fazenda.ativo_inativo)
    const [areasOp, setAreasOp] = useImmer([])
    const [errors, setErrors] = useState({});

    useEffect(() => {
        setNome(fazenda.nome_da_fazenda);
        setProp(fazenda.proprietario);
        setMunicipio(fazenda.municipio);
        setCodigo(fazenda.codigo_da_fazenda);
        setLatitude(fazenda.latitude);
        setLongitude(fazenda.longitude);
        setArea(fazenda.id_area && fazenda.area_de_operacao ? {
            id: fazenda.id_area,
            name: fazenda.area_de_operacao
        } : null);
        setStatus(fazenda.ativo_inativo);

    }, [fazenda])

     // area_de_operacao: "Piracicaba"
    // ativo_inativo: "ATIVO"
    // codigo_da_fazenda: "1"
    // data_do_cadastro: "3/9/2021"
    // hora_do_cadastro: "15:18:43"
    // id_area: "1"
    // id_fzd: 1
    // latitude: "-22.716109646175926"
    // longitude: "-47.64023275435203"
    // municipio: "Piracicaba"
    // nome_da_fazenda: "Fazenda da Alegria"
    // proprietario: "1"

    const now = new Date();

    useEffect(()=> {
        axios.get(URL+'selecionar_todos_proprietarios')
        .then(res=>{
            if(!res.data) return

            const optionsOpRequest = res.data.map((item)=>({
                id: item.id_prop,
                name: item.nome_do_proprietario
            }))
            setOptions(optionsOpRequest)          
        })
        .catch(err=>console.log(err))
    },[])

    useEffect(()=>{
        axios.get(URL+'todas_areas')
        .then(res=>{
                const itensAreas = res.data
                const optionsAreasRequest = itensAreas.map((item)=>({
                    id:item.id,
                    name: item.nome_da_area
                }))
                setAreasOp(optionsAreasRequest)
        })
        .catch(err=>console.log(err))
      },[])

    function submit(event){
        event.preventDefault();

        let hasError = false;
        let validations = {};
        const latiLongRegExp = new RegExp(`^(\\-?\\d+(\\.\\d+)?)`) 
       
        if(!latiLongRegExp.test(latitude)){
            validations = { ...validations, latitude: true } 
            hasError = true;         
        }

        if(!latiLongRegExp.test(longitude)){
            validations = { ...validations, longitude: true }  
            hasError = true;         
        }
         
        setErrors({ ...errors, ...validations }) 
        if(hasError) return;

        setLoad(true);
        axios.put(URL+'atualizar_fazenda',{
            id_fzd: fazenda.id_fzd,
            nome_da_fazenda: nome,
            proprietario: prop,
            area_de_operacao: area ? area.name : "",
            id_area: area ? area.id : "", 
            municipio: municipio,
            latitude: latitude,
            longitude: longitude,
            codigo_da_fazenda: codigo,
            data_do_cadastro:now.getDate()+'/'+now.getMonth()+'/'+now.getFullYear(),
            hora_do_cadastro: now.getHours()+':'+now.getMinutes()+':'+now.getSeconds(),
            ativo_inativo : status
        })
        .then(res=>{
            setLoad(false);
            refresh()          
            Swal.fire(nome,'cadastrado com sucesso!','success')
            onHide()
        })

        .catch(err=>{
            console.log(err)
            setLoad(false)
        })
    }

    const propAtual = options.find((p)=> p.id === parseInt(prop))
    console.log("Fazenda:",fazenda)
    
  return (
    <>
        <Modal              
            show={open}
            onHide={onHide}
            aria-labelledby="example-modal-sizes-title-lg"
            contentClassName='dialogModal'          
        >
            <Modal.Header>
                <Modal.Title>
                    <h5 style={{fontWeight:'bolder'}}>Editar fazenda</h5>
                </Modal.Title>
                <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={onHide} style={{fontSize:25, color:'#868686'}}/></Button>
            </Modal.Header>
            <Modal.Body>
                <Form className="" onSubmit={submit}>
                    <Row className="g-2">
                        <Col md>
                            <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Nome da Fazenda <font className='obr'>*</font></Form.Label>
                                <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" value={nome} onChange={e => setNome(e.target.value)} required/>
                            </Form.Group>
                        </Col>
                        <Col md>
                            <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Proprietário <font className='obr'>*</font></Form.Label>
                                <Typeahead
                                    id="basic-typeahead-single"
                                    labelKey="name"
                                    onChange={selected=>{
                                        if(selected && selected.length){
                                            setProp(selected[0].id)
                                        } 
                                        else{
                                            setProp("")
                                        }
                                    }}
                                    options={options}
                                    placeholder="Digite aqui"
                                    className='customInput shadow'
                                    selected={ propAtual ? [propAtual] : []}
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row className="g-2">
                        <Col md>
                            <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Município <font className='obr'>*</font></Form.Label>
                                <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" value={municipio} onChange={e => setMunicipio(e.target.value)} required/>
                            </Form.Group>
                        </Col>
                        <Col md>
                            <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Área de Atuação <font className='obr'>*</font></Form.Label>
                                <Typeahead
                                    id="basic-typeahead-single"
                                    labelKey="name"
                                    onChange={selected=>{
                                        if(selected && selected.length){
                                            setArea(selected[0])
                                        } 
                                        else{
                                            setArea(null)
                                        }                                   
                                    }}
                                    options={areasOp}
                                    placeholder="Digite aqui"
                                    className='customInput shadow'
                                    selected={ area ? [area] : null}
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row>
                        <Col md>
                            <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Latitude <font className='obr'>*</font></Form.Label>
                                <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" value={latitude} onChange={e=>{ setLatitude(e.target.value); setErrors({ ...errors, latitude: false })}} required/>
                                {errors.latitude && <p className="formError">Latitude inválida</p>}                          
                            </Form.Group>
                        </Col>
                        <Col md>
                            <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Longitude <font className='obr'>*</font></Form.Label>
                                <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" value={longitude} onChange={e=>{ setLongitude(e.target.value); setErrors({...errors, longitude: false})}} required/>
                                {errors.longitude && <p className="formError">Longitude inválida</p>} 
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row className="g-2">
                        <Col md={6}>
                            <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Código da Fazenda <font className='obr'>*</font></Form.Label>
                                <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" value={codigo} onChange={e=>setCodigo(e.target.value)} required/>
                            </Form.Group>
                        </Col>
                        <Col md>
                            <Form.Group className="mb-3" controlId="formBasicEmail">
                                <Form.Label className='customLabel'>Status <font className='obr'>*</font></Form.Label>
                                <select  className='customInput shadow form-control' value={status} onChange={e=>setStatus(e.target.value)}>
                                    <option selected value="ATIVO">ATIVO</option>
                                    <option value="INATIVO">INATIVO</option>       
                                </select>
                            </Form.Group>
                        </Col>
                    </Row>
                    <div class="col text-center mt-3">
                        <Button className='blueColor btnCadastro' type="submit" size='lg'>
                            {load 
                                ? 
                                <Spinner animation="border" />
                                :
                                'Editar Fazenda'
                            }
                        </Button>
                    </div>
                </Form>
            </Modal.Body>
        </Modal>
    </>
  );
}

export default FormsEditFazenda;