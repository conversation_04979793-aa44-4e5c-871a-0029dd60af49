import React,{useState,useEffect} from 'react'
import MenuIcon from '@material-ui/icons/Menu';
import axios from 'axios';
import { <PERSON>, Row, Col, Spin<PERSON>,<PERSON>, <PERSON>ton } from 'react-bootstrap';
import { Menu, MenuItem } from '@material-ui/core';
import { IoClose } from "react-icons/io5"
import { useUserContext } from '../../../context/authContext';
import { URL } from '../../../services';
import FormsNewViatura from './FormsNewViatura';
import EditVehicalForm from './EditVehicalForm';
import ViaturaDefaultImg from '../../../assets/img/bg-11.jpg'
import ReactTooltip from 'react-tooltip';
import { Avatar, TableRow, TableCell, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../components/organisms';
import { BsFillPersonCheckFill } from "react-icons/bs";
import { AiOutlineUserDelete } from "react-icons/ai";
import { FaRegEdit } from "react-icons/fa";



function VehicalSingle({data, page, rowsPerPage}) {
   
    const [viaturaEditar, setViaturaEditar] = useState({});
    const {toggle,settoggle} = useUserContext();

    function submit(viatura){
        setOpen(false)
        axios.put(URL+'atualizar_viatura',{
            id_viat: viatura.id_viat,
            modelo_viat: viatura.modelo_viat,
            placa: viatura.placa,
            km_inicial: viatura.km_inicial,
            tipo_combustivel: viatura.tipo_combustivel,
            unidade: viatura.unidade,
            situacao_vtr: viatura.situacao_vtr,
            setor: viatura.setor,
            area_atuacao: viatura.area_atuação,
            imagem_viat: viatura.imagem_viat.name || viatura.imagem_viat ,
            ativa_inativa: viatura.ativa_inativa==="ATIVO"?"INATIVO":"ATIVO"

        })
        .then(res=>{
            console.log(res)
            setanchor(null)
            settoggle(!toggle)
        })
        .catch(err=>{
            console.log(err)
        })
    }
    
    const[open,setOpen] = useState(false);
    const [anchor, setanchor] = useState(0)
    const handleDelete=()=>{
        setanchor(null)
    }

    const handleEdit = (viaturaEditar) => {
        setanchor(null);
        setOpen(true);
        setViaturaEditar(viaturaEditar);
    }

    const showModal=()=>{
        return (<EditVehicalForm oopen={[open,setOpen]} data={viaturaEditar}/>)
    }

    
    return (
        <>
        {data?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index) =>(
            <TableRow key={index}>
                <TableCell><Avatar alt={i.modelo_viat} src={i.imagem_viat || ViaturaDefaultImg}/></TableCell>
                <TableCell align='center'>
                    <h6 style={{fontWeight:'bold'}}>#{i.id_viat}</h6>
                    <h6>{i.modelo_viat}</h6>
                </TableCell>
                <TableCell align='center'><h6>{i.km_inicial ? i.km_inicial: "----"} KM</h6></TableCell>
                <TableCell align='center'><h6 style={{lineHeight:4.5}}> -- </h6></TableCell>
                <TableCell align='center'>
                    <ReactTooltip place="bottom" className="text" id={`tooltip-${i.id_viat}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{i.ativa_inativa === "ATIVO" ? "ATIVO" : "INATIVO"}</span>          
                    </ReactTooltip>
                    <div 
                        data-tip 
                        data-for={`tooltip-${i.id_viat}`}
                        style={{
                            width:'10px',
                            height:'10px', 
                            backgroundColor:i.ativa_inativa==="ATIVO"?'rgba(0, 169, 86, 1)':"#ff0000ba", 
                            borderRadius:'3px',
                            marginLeft: '15px'
                        }}>
                    </div>
                </TableCell>
                <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem onClick={() => handleEdit(i)}>
                            <ListItemIcon><FaRegEdit size={20}/></ListItemIcon>
                            Editar
                        </MenuItem>
                        {i.ativa_inativa==="ATIVO" 
                            ?
                                <MenuItem onClick={() => submit(i)}>
                                    <ListItemIcon><AiOutlineUserDelete size={20}/></ListItemIcon>
                                    Desativar
                                </MenuItem>
                            : 
                                <MenuItem onClick={() => submit(i)}>
                                    <ListItemIcon><BsFillPersonCheckFill size={20}/></ListItemIcon>
                                    Ativar
                                </MenuItem>
                        }
                    </TableItemMenuMulti>
                </TableCell>
            </TableRow>
        ))}
        <Modal
            show={open}
            onHide={() => setOpen(false)}
            aria-labelledby="example-modal-sizes-title-lg"
            contentClassName='dialogModal'
        >         
            <Modal.Header>
                <Modal.Title>
                <h5 style={{fontWeight:'bolder'}}>Editar Viatura</h5>
                </Modal.Title>
                <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>setOpen(false)} style={{fontSize:25, color:'#868686'}}/></Button>
            </Modal.Header>
            <Modal.Body>
                {showModal()}
            </Modal.Body>
                
        </Modal>
        </>
    )
}

export default VehicalSingle
