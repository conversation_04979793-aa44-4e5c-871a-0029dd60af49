import React from 'react';
import Swal from 'sweetalert2'
import axios from 'axios'

import './ColumnTitleDescriptionRender.css';
import { NavDropdown, Row, Col } from 'react-bootstrap';
import ReorderIcon from '@material-ui/icons/Reorder';
import ArrowForwardIosIcon from '@material-ui/icons/ArrowForwardIos';
import { URL_API } from '../../../services';



const ColumnNavDropdownRender = (id, openEditModal, update) =>{  
  const handleClickEdit = row =>{
    openEditModal(row)
  }

  const handleClickDelete = row => {
    if(row.ativa_inativa == "inativa") {
      Swal.fire("Esta área já está inativa.")
    } else {
      Swal.fire({
        title:'Excluir área',
        text:'Deseja excluir esta área ?',
        icon:'question',
        showCancelButton: true,
        cancelButtonText: 'Não',
        confirmButtonText: `Sim`,
      }).then(async (result) => {
        if (result.isConfirmed) {
          const dataToSend = {...row, ativa_inativa: "inativa"}
      
          const response = await axios.put(URL_API+"atualizar_area", dataToSend)
          if(response.data == "OK") {
            update()
          }
        }
      })
    }
  }

  return {         
      id: id,
      cell: (row) => {          
          return (    
            <>        
              <NavDropdown title={<ReorderIcon color="disabled"/>} id="basic-nav-dropdown">                         
                <NavDropdown.Item >Ações</NavDropdown.Item>

                <NavDropdown.Divider />

                <NavDropdown.Item  onClick={() => handleClickEdit(row)}>
                  <ArrowForwardIosIcon style={{ fontSize: '1rem', marginRight: '10px' }} />
                  Editar
                </NavDropdown.Item>

                {/* <NavDropdown.Item  onClick={() => handleClickDelete(row)}>
                  <ArrowForwardIosIcon style={{ fontSize: '1rem', marginRight: '10px' }} />
                  Excluir
                </NavDropdown.Item> */}

              </NavDropdown> 
            </>                     
          )
      }
  }
}

export default ColumnNavDropdownRender; 
