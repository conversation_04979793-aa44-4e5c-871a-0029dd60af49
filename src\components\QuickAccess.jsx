import React from 'react'
import { Card, Row, Col, Spinner } from 'react-bootstrap';
import CustomButton from '../components/CustomButton';
import CustomButtonCriar from '../components/CustomButtonCriar';
// import { experimentalStyled as styled } from '@mui/material/styles';
// import Box from '@mui/material/Box';
// import Paper from '@mui/material/Paper';
// import Grid from '@mui/material/Grid';

const styles = {
    card: {
        overflow:'hidden',
        width: '400px',
        height: '393px',
        borderRadius: '11px',
        fontWeight: '400', 
        backgroundColor: 'rgba(255, 255, 255, 1)', 
        boxShadow: '0px 0px 10px rgba(170, 170, 170, 0.***************)',
    },
    cardHeader: {   
        paddingTop:'20px',
        border:'1px solid',
        fontWeight: '500', 
        height: '126px',
        backgroundColor: 'rgb(0, 94, 154)', 
        color: 'rgba(232, 230, 227, 0.84)' ,
    },
    access: {
        padding:'20px ',
        marginTop: '200px ',
        fontWeight: "700",
        fontSize: '16px',
        color: 'rgba(255, 255, 255, 0.843137254901961)',
    },
};


function QuickAccess() {

    return (
        
            <div style={ styles.card } >
                <div style={ styles.cardHeader }>
                    <span style={ styles.access }>
                        Acesso rápido
                    </span>
                </div>
                <div className='mx-auto menu'>
                    <Row style={{justifyContent:'space-evenly'}}>

                        <CustomButton tipo='policiais'/>
                        <CustomButton tipo='rotas'/>
                        <CustomButton tipo='fazendas'/>
                    </Row>
                </div>
            </div>
        
    )
}

export default QuickAccess;
