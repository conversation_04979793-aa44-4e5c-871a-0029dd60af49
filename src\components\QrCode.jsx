import React, {useEffect, useState} from 'react';

import { <PERSON><PERSON><PERSON>,<PERSON>, Col, <PERSON>, <PERSON>, Spinner } from 'react-bootstrap'
// import Menu from '../components/menuRapido';

import { HiOutlineMail } from 'react-icons/hi'
import { MdPrint } from 'react-icons/md'
import axios from 'axios';
import { URL } from '../services';

var QRCode = require('qrcode.react');

function QrCode(props) {

    const [gerado, setGerado] = useState(false);
    const [value, setValue] = useState({});
    const [loading, setLoading] = useState(false);

    useEffect(()=>{
        setLoading(true)
        axios.get(URL+'todas_qrcodes')
        .then(res=>{
            setLoading(false)

            let dataFilter = res.data.filter(data => data.id_fzd == props.id);

            if(dataFilter.length > 0){
                setValue(dataFilter[0])
                setGerado(true);
            }
        })
    },[])
    
    const handleGerarClick = () =>{
        setLoading(true)
        const now = new Date();
        axios.post(URL+'salvar_qrcode',{
            id_fzd: props.id,
            nome_da_fazenda: props.nomeFazenda,
            data_da_criacao: now.getDate()+'/'+(now.getMonth()+1)+'/'+now.getFullYear(),
            hora_da_criacao: now.getHours()+':'+now.getMinutes()+':'+now.getSeconds()

        })
        .then(res=>{
            if(res.status == 200){
                setValue(res.data)
                setGerado(true)
                setLoading(false)
            }
        })
        .catch(err=>{
            console.log(err)
        })
    }
    
    const downloadQRCode = () => {
        // Generate download with use canvas and stream
        const canvas = document.getElementById("canvas");
        const pngUrl = canvas
          .toDataURL("image/png")
          .replace("image/png", "image/octet-stream");
        let downloadLink = document.createElement("a");
        downloadLink.href = pngUrl;
        downloadLink.download = 'QRCode_'+props.nomeFazenda+'.png';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      };

  return (
    <>
        {loading 
            ? 
                <Spinner/>
            : 
                <>
                    {gerado 
                        ? 
                        <Card className='mb-5 cardMap shadow'>
                            <h5 className='mx-auto text-dark mt-3'>QR Code da Fazenda</h5>
                            <QRCode id="canvas" className='mx-auto mt-2' style={{width:250, height:250}} value={value?.id_fzd} />
                            <div >
                                <center>
                                    <div className='mt-3'>
                                        <HiOutlineMail className='mx-3 text-dark iconQR'/>
                                        {/* <AiOutlineInstagram className='mx-3 text-dark iconQR'/>
                                        <ImWhatsapp className='mx-3 text-dark iconQRW'/> */}
                                        <a href='#' onClick={downloadQRCode}><MdPrint className='mx-3 text-dark iconQR'/></a>
                                    </div>
                                </center>
                            </div>
                            <Card.Footer className='footerQr mt-3'>
                                <div className='mx-auto text-light'>
                                    <Row>
                                        <Col className='ml-5'>
                                            <center><p><font style={{fontSize:13, fontWeight:'lighter'}}>Gerado em </font><br/>
                                            <font style={{fontSize:20, fontWeight:'bolder'}}>{value?.data_da_criacao}</font></p></center>
                                        </Col>
                                        <Col>
                                            <center><p><font style={{fontSize:13, fontWeight:'lighter'}}>Validade</font><br/>
                                            <font style={{fontSize:20, fontWeight:'bolder'}}>133 dias</font></p></center>
                                        </Col>
                                    </Row>
                                </div>
                            </Card.Footer>
                        </Card>
                        :
                        <Button className='botaoQrCode mb-5 bolder' variant='dark' onClick={handleGerarClick}>Gerar QR Code</Button>
                    }      
                </>
        }
    </>
  );
}

export default QrCode;