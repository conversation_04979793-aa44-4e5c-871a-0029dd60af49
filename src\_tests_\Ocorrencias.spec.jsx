import * as React from 'react'
import { render} from '@testing-library/react';
import AppTest from './AppTest';
import Ocorrencias from '../containers/Ocorrencias';
import PanicMap from '../components/PanicMap';
import OcorrenciasList from '../components/OcorrenciasList';


describe('Verificando  A Tela de Ocorrencias', () => {
  it('Reinderize a Tela de Ocorrencias', () => {
    render(<AppTest><Ocorrencias/></AppTest>);

    const {container} = render(<AppTest><PanicMap/></AppTest>)

    expect(container).toBeTruthy();
  })

  it('Verificando Se há o o componente está aparecendo para o usuário', () => {
    render(<AppTest><Ocorrencias/></AppTest>);

    const {container} = render(<AppTest><OcorrenciasList/></AppTest>)

    expect(container).toBeTruthy();
  })
  
})