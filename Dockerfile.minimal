# Minimal CPU usage Dockerfile - Uses Node 16 for better compatibility
# and avoids most native compilation issues

# Build Stage
FROM node:16-alpine AS build

# Install only git (minimal dependencies)
RUN apk add --no-cache git

WORKDIR /app

# Copy package files
COPY package*.json ./

# Configure npm for minimal resource usage
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm config set audit false && \
    npm config set fund false && \
    npm config set optional false

# Use smaller memory allocation (Node 16 doesn't need legacy OpenSSL)
ENV NODE_OPTIONS="--max-old-space-size=1024"

# Install with minimal flags to reduce CPU usage
RUN npm ci --legacy-peer-deps --no-audit --no-fund --no-optional --maxsockets=1

COPY . .

# Build with reduced parallelism
ENV CI=true
RUN npm run build

# Production Stage
FROM nginx:stable-alpine AS production

# Minimal nginx setup
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/build /usr/share/nginx/html

# Simple healthcheck without additional dependencies
HEALTHCHECK --interval=60s --timeout=3s --start-period=5s --retries=2 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
