import React,{useState, useEffect} from 'react';
import BgImage from '../assets/img/bg-11.jpg'
import styled from 'styled-components'
import { useUserContext } from '../context/authContext'
import { makeStyles } from '@material-ui/core/styles';
import TextField from '@material-ui/core/TextField';
import { Button,  Row, Col, Container, Modal, Form, Spinner   } from 'react-bootstrap'
import { URL_API } from '../services';
import axios from 'axios';
import Swal from 'sweetalert2'
import Aiba from '../assets/img/AIBA (1) (3).jpg'

const ClickableDiv = styled.div`
  border: 1px solid #999;
  border-radius: 6px;
  padding: 10px;
  width: 50%;
  margin: 20px auto;
  cursor: pointer;

  &:hover {
    color: white;
    background-color: #bbb3b3;
  }
`;

const useStyles = makeStyles((theme) => ({
  root: {
    '& > *': {
      margin: theme.spacing(1),
      width: '25ch',
    },
  },
}));

var QRCode = require('qrcode.react');

function Login() {
  
  const { handleUserLogin, setUserRole, bloqueado, show, handleCodigo, loading } = useUserContext();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [token, setToken] = useState("");
  const [codigo, setCodigo] = useState('');
  
 
  const classes = useStyles();

  async function getToken(){
    const response = await axios.get(URL_API+'api')
    setToken(response.data.key);
    window.localStorage.setItem('@Token', response.data.key);
  }
  //verifica se o user tem o id armazenado no cache do celular, se não tiver fica na tela de login
  function auth(){
    const ident = window.localStorage.getItem('@Ident');
    if(ident){
      window.location.href = "/home"
    }
    else{
      getToken();
    }
  }

  useEffect(()=>{
    auth();
  },[]);

  useEffect(()=>{
    if(show){
      Swal.fire({
        title: 'Enviamos um SMS para você!',
        input: 'text',
        inputLabel: 'Por favor e informe o código de segurança',
        showCancelButton: true,
        inputValidator: code => {
          handleCodigo(code)
        }
      })
    }
  },[show])

  function handleSubmit(event) {
    event.preventDefault();
    handleUserLogin(email, password, token)
  }

  function validateForm() {
    return email.length > 0 && password.length > 0 && !bloqueado;
  }

  return (
    <Container style={{backgroundColor:'#fafafa'}}>
      <div>
        <Row className="justify-content-md-center mx-auto">
          {/* <h1 className="mt-5">AIBA</h1> */}
          <img src={Aiba} style={{minWidth: '354px', maxWidth: '354px', marginTop:'20px'}}/>
        </Row>
        <Row className="justify-content-md-center mx-auto">
        <div className="card mt-5" style={{minWidth: '354px', maxWidth: '354px'}}>
            <div className="card-body">
              <center className="mt-4">
                <form className={classes.root} onSubmit={handleSubmit} autoComplete="off">
                  <TextField
                    inputProps={{ "data-testid": "user-field" }} 
                    id="standard-basic" 
                    label="User" 
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                  <TextField 
                    inputProps={{ "data-testid": "password-field" }}
                    id="standard-basic" 
                    type="password" 
                    label="Senha" 
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  {loading 
                    ? 
                    <Button variant="primary" disabled><Spinner animation="border" variant="light" /></Button>
                    : 
                    <Button  data-testid="form-btn" variant="primary" type="submit" disabled={!validateForm()}>Entrar</Button>
                  }
                </form>
              </center>     
            </div>
          </div> {/* Termina card */}
        </Row>
        <Row className="text-center mx-auto">
          <h3 className="mt-5">Para baixar o aplicativo acesse:</h3>
        </Row>
        <Row className="justify-content-md-center mx-auto">
          <QRCode id="canvas" className='mx-auto mt-2' style={{width:180, height:150}} value={'https://drive.google.com/file/d/1gG4UgQZPY1nhPA9t8iqKe0xRG7_msrzW/view?usp=sharing'} />
        </Row>
        </div>
      </Container>
  );
}

export default Login;