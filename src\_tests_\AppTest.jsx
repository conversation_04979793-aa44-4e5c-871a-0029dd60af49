import React from 'react';
import { SocketContext, socket } from '../context/socketContext';
import { UserProvider } from '../context/authContext';
import { AlertProvider } from '../context/alertContext';
import { PositionProvider } from '../context/locationContext';
import { MapProvider } from '../context/mapContext';

window.URL.createObjectURL = () => {}


function AppTest({children}) {
  
  return (
    <SocketContext.Provider value={socket}>
      <AlertProvider>
        <UserProvider>
          <PositionProvider>
            <MapProvider >
             {children}
            </MapProvider>
          </PositionProvider>
        </UserProvider>
      </AlertProvider>
    </SocketContext.Provider>
  );
}

export default AppTest;