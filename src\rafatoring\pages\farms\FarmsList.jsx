import React, { useEffect, useState } from 'react';
import { useMapContext } from '../../../context/mapContext';
import { TableComponent } from '../../components/organisms';
import { TableHead, TableRow, TableCell, TableBody } from '@mui/material';
import axios from 'axios';
import { URL } from '../../../services';
import FarmSingle from './FarmSingle';

const FarmsList = ({areas}) => {
const [fazendas, setFazendas] = useState([])
const [page, setPage] = useState(0);
const [rowsPerPage, setRowsPerPage] = useState(5);

const {setIdArea} = useMapContext();

const getFazendas = () =>{
  axios.get(URL+'todas_fazendas')
  .then(res=>{
      const fzds = res.data 
      console.log(res.data)
      fzds.map((data, index)=>{
        const date = data.data_do_cadastro.includes('-') ? data.data_do_cadastro.split('-') : data.data_do_cadastro.split('/')
        if(date[0].includes('/')){
          let novaData = date[0].split('/');
          data.data_do_cadastro = novaData[0]+'-'+novaData[1]+'-'+novaData[2] 
        }
        else if(date[1].includes('/')){
          let novaData = date[1].split('/');
          data.data_do_cadastro = novaData[0]+'-'+novaData[1]+'-'+novaData[2] 
        }
            else if(date[2].includes('/')){
          let novaData = date[2].split('/');
          data.data_do_cadastro = novaData[0]+'-'+novaData[1]+'-'+novaData[2] 
        }
        else{
          data.data_do_cadastro =  date[0] > 2000 ? date[2]+'-'+date[1]+'-'+date[0] : date[0]+'-'+date[1]+'-'+date[2]
        }
      })
      fzds.sort((a,b) => {
        if(new Date(a.data_do_cadastro) < new Date(b.data_do_cadastro)){
          return 1 

        }else {
          return -1
        }
      }) 

      const tempFZD = [...fzds]
      setFazendas(tempFZD)
    })
    .catch(err=>console.log(err))
  }


  useEffect(() => {
    getFazendas();
  },[])

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

    return (  
        <>
            {fazendas
                ?
                <TableComponent 
                    count={fazendas.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Listagem de Fazendas'
                >
                    <TableHead>
                        <TableRow>
                            <TableCell>Cadastro</TableCell>
                            <TableCell align='center'>Fazenda/Proprietário</TableCell>
                            <TableCell align='center'>Área de Atuação</TableCell>
                            <TableCell align='center'>Status</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        <FarmSingle data={fazendas} page={page} rowsPerPage={rowsPerPage} areas={areas} refresh={getFazendas}/>
                    </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>             
    ); 
}

export default FarmsList;



