import React from 'react'
import { Card, Row, Col, Spinner } from 'react-bootstrap';


function AlertStats({color,data,title}) {
    return (
        <>
            <Card style={{width:"300px",height:'180px',borderRadius:"10px"}} className='shadow m-2 ' id='cardMap'>
                <Card.Body>
                <Card.Title style={{fontWeight:'100',color:'#8F7F93',fontSize:'14px'}}>
                        {title}
                    </Card.Title>
                    <Card.Text className={color} style={{fontSize:'18px', font: 'ArialMT', fontWeight:'500', fontStyle: 'normal' }}>
                        {data.map((e)=>(
                            <div style={{display:'flex',width:'100%',justifyContent:'space-between',paddingRight:'10px',paddingLeft:'10px'}}>{e.text}<span>{e.num}</span></div>)
                        )}
                    </Card.Text>
                </Card.Body>
            </Card>
        </>

    )
}

export default AlertStats
