import React, { useState, useEffect } from 'react'
import Header from '../components/Header'
import Map from '../components/Mapa'
import SearchBar from 'material-ui-search-bar'
import SingleListItem from '../components/PoliceRoutes2/SingleListItem'
import { URL } from '../services/index'
import Footer from '../components/Footer'
import axios from 'axios'
import Swal from 'sweetalert2'


function PoliceRoutes2() {


    const [state, setstate] = useState("")
    const [color, setcolor] = useState(0)
    const [edit, setEdit] = useState(localStorage.getItem("edit"))
    const [ID, setID] = useState(localStorage.getItem("@Ident"))
    const [areasAtuacao, setAreasAtuacao] = useState()
    const [search, setsearch] = useState({ value: "" })

    function submit(event) {
        const data = localStorage.getItem("routes").replace("[", "").replace("]", "")
        console.log(localStorage.getItem("routes").length)
        const now = new Date();
        if (localStorage.getItem("routes").length > 2) {
            axios.post(URL + 'salvar_rota', {
                "data_criacao": now.getDate() + '/' + now.getMonth() + '/' + now.getFullYear(),
                "hora_criacao": now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds(),
                "id_area": localStorage.getItem("areas"),
                "id_viat": "",
                "unidade": "",
                "data_inicio": "",
                "hora_inicio": "",
                "lista_fazendas": data,
                "kilometragem_estimada": "",
                "componentes_policiais": "",
                "status": "Aguardando"
            })
                .then(res => {
                    console.log(res)
                    Swal.fire('Sucesso!')
                    localStorage.setItem('areas', "")
                    localStorage.setItem('routes', "[]")
                    window.location.href='/rotas'
                })
                .catch(err => {
                    console.log("error")
                })
        }
    }
    function submit1(event) {
        const data = localStorage.getItem("routes").replace("[", "").replace("]", "")
        console.log(localStorage.getItem("routes").length)
        const now = new Date();
        if (true) {
            axios.put(URL + 'atualizar_rota', {
                "id": edit,
                "data_criacao": now.getDate() + '/' + now.getMonth() + '/' + now.getFullYear(),
                "hora_criacao": now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds(),
                "id_area": localStorage.getItem("areas"),
                "id_viat": "",
                "unidade": "",
                "data_inicio": "",
                "hora_inicio": "",
                "lista_fazendas": data,
                "kilometragem_estimada": "",
                "componentes_policiais": "",
                "status": "Aguardando"
            })
                .then(res => {
                    localStorage.setItem("edit", "")
                    console.log(res)
                    Swal.fire('success')
                    localStorage.setItem('areas', "")
                    localStorage.setItem('routes', "[]")
                    window.location.reload()
                })
                .catch(err => {
                    console.log("error")
                })
        }
    }
     
    useEffect(()=>{
        if(localStorage.getItem("areas")){
            axios.post(URL+'buscar_fazendas_atuacao',
            {id:localStorage.getItem('areas')}
            ).then(res=> {
                const fazendas = res.data
                //Ordem das fazendas na tabela de rota.
                fazendas.sort((a,b) => {
                    if(new Date(a.data_do_cadastro) < new Date(b.data_do_cadastro)){
                        return 1
                    } else {
                        return -1 
                    }
                })
                setstate(res.data)
            })
            .catch(err=>console.log("not found",err))
        }
        else {
            window.location = '/PoliceRoutes'
        }

        async function fetchData(){
            const areasResponse = await axios.get(URL+"todas_areas")
            setAreasAtuacao(areasResponse.data)   
        }
        fetchData();

    }, [])

    return (
        <>
        <Header />
        <div style={{display:'flex',fontFamily:'sans-serif',width:'100%',height:'700px',flexWrap:'wrap',marginTop:'60px'}}>
            <div style={{width:'45%',display:'flex',justifyContent:'flex-end'}}>
                <div style={{display:'flex',width:'500px'}}>
                    <Map areasAtuacao={areasAtuacao} height={650} largura={"100%"}/>
                </div>
            </div>   
            <div style={{fontFamily:'sans-serif',padding:'40px',width:'55%'}}>
                
                <div style={{ fontFamily: 'sans-serif', padding: '40px', width: '55%' }}>
                    <div style={{ display: 'flex', height: '600px', flexDirection: 'column', width: '600px' }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <h5 style={{ fontSize: '24px', fontFamily: "Trebuchet MS" }}>Escolha as fazendas da rota</h5>
                            <SearchBar value={search.value} onCancelSearch={() => { setsearch({ value: '' }) }} onRequestSearch={() => window.location = "#"} onChange={(value) => setsearch({ value: value })}
                                style={{

                                    height: '30px',
                                    backgroundColor: '#F2F2F2',
                                    maxWidth: 200,
                                    borderRadius: '20px'
                                }}
                            />
                        </div>
                        <div style={{ marginTop: '40px', color: '#B2A9A9', paddingLeft: '15px', display: 'flex', width: '600px' }}>
                            <p style={{ width: '25%', fontSize: '12px' }}>Fazenda</p>
                            <p style={{ width: '25%', fontSize: '12px' }}>Localização</p>
                            <p style={{ width: '25%', fontSize: '12px' }}>Visitas Recebidas</p>
                            <p style={{ width: '25%', marginLeft: '20px', fontSize: '12px' }}>Extras</p>
                        </div>

                        <div style={{ height: '100%', overflow: 'auto', width: '100%' }}>
                            {
                                state ? state.map((e) => (
                                    <SingleListItem color={color} data={e} />
                                )) : "Loading"
                            }
                            <div style={{ position: 'relative', marginBottom:'20px', marginLeft: "350px"}}>                        
                                {!edit && ID==="ADMINPM" ?
                                    <button onClick={submit} style={{ border: 'none', width: '200px', height: '55px', backgroundColor: '#0076C1', color: 'white', borderRadius: "40px" }}>
                                        Criar Rota
                                    </button> :
                                    <button onClick={submit1} style={{ border: 'none', width: '200px', height: '55px', backgroundColor: '#0076C1', color: 'white', borderRadius: "40px" }}>
                                        Edit Route
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Footer />
        </div>
        </>
    )
}

export default PoliceRoutes2