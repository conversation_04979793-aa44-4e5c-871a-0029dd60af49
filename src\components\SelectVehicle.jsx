import axios from 'axios';
import React,{useEffect, useState} from 'react';
import { 
  Container,
  Row, 
  Col, 
  Card, 
  Form, 
  Button, 
  InputGroup,
  FormControl,
  Spinner
} from 'react-bootstrap'
import { URL_API } from '../services';
import VehiclePush from './VehiclePush';


const SelectVehicle = (props) =>{

  const [viaturas, setViaturas] = useState([]);

  async function fetchDataViaturas(){
    const response = await axios.get(URL_API+"todas_viaturas")
    setViaturas(response.data)
  }

  useEffect(()=>{
    fetchDataViaturas()
  },[])

  return(
    <>
      <Container className="g-1">
        {viaturas.map((item, index) =>(
          <VehiclePush
            key={index}
            viaturas={setViaturas}
            data={item}
            idOcorrencia={props.idOcorrencia}
            fetchOcorrencias={props.fetchOcorrencias}
          />
        ))}
      </Container>  
    </>
  )
}

export default SelectVehicle;