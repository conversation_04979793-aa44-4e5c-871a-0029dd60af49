import React,{useState, useEffect} from 'react'
import { Card, Row, Col, Spinner } from 'react-bootstrap';
import Map from './Mapa';
import OwnerCard from './OwnerCard';
import CustomButton from '../components/CustomButton';
import CustomButtonCriar from '../components/CustomButtonCriar';
import axios from 'axios';
import { URL_API } from '../services';
// import { experimentalStyled as styled } from '@mui/material/styles';
// import Box from '@mui/material/Box';
// import Paper from '@mui/material/Paper';
// import Grid from '@mui/material/Grid';

// const Item = styled(Paper)(({ theme }) => ({
//     ...theme.typography.body2,
//     padding: theme.spacing(2),
//     textAlign: 'center',
//     color: theme.palette.text.secondary,
//   }));

function MapAndList() {

    const [areasAtuacao, setAreasAtuacao] = useState([]);
    const [proprietarios, setProprietarios] = useState([]);

    const fetchTodasAreas = async () => { 
        const response = await axios.get(URL_API+"todas_areas")
        setAreasAtuacao(response.data)
    }

    const fetchProprietarios = async () => {
        const response = await axios.get(URL_API+'selecionar_todos_proprietarios')
        setProprietarios(response.data)
    }

    useEffect(()=>{  
        fetchTodasAreas();
        fetchProprietarios();
    },[])

    return (
        <>
        <Row style={{maxWidth:"1250px",maxHeight:"956px",overflow:'auto',minWidth:'1100px'}}>
            <Col style={{minWidth:"50%",alignItems:'center'}}>
                <Map areasAtuacao={areasAtuacao} largura="600px" height="860px" policias={false}/>
                <div className='mx-auto menu'>
                    <Row style={{justifyContent:'space-evenly'}}>
                        <CustomButtonCriar tipo='proprietario'/>
                        <CustomButton tipo='fazendas'/>
                    </Row>
                </div>
            </Col>
            <Row style={{width:"50%",justifyContent:'center',overflow:'auto'}}>
                {proprietarios.map((proprietario)=>{
                    return (<OwnerCard data={proprietario} refresh={fetchProprietarios} />)
                })}
            </Row>
        </Row>
        </>
    )
}

export default MapAndList;
