import React, {useContext, useEffect, useState} from 'react';
import FormsNewFazenda from './FormsNewFazenda';
import { IoClose } from "react-icons/io5"

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-bootstrap'
import FormsNewUser from './FormsNewUser';
import FormsNewViatura from '../rafatoring/pages/vehicals/FormsNewViatura';
import FormsNewProprietario from './FormsNewProprietario';

import {AiOutlinePlusCircle} from  'react-icons/ai'
import FormsNewArea from './FormsNewArea';
import { useHistory } from 'react-router';
import SelectVehicle from './SelectVehicle';


function CustomButtonCriar(props) {
  const[open,setOpen] = useState(false);
  const [fluxo, setFluxo] = useState();
  const history = useHistory();

  const handleTitle = () =>{
    switch(props.tipo){
      case 'policiais':
        return (
          'Criar novo Usuário'
        )
      case 'fazendas':
        return (
          'Criar nova Fazenda'
        )
      case 'viatura':
        return (
          'Criar nova Viatura'
        )
      case 'proprietario':
        return (
          'Criar novo Proprietário'
        )
      case 'areas':
        return (
          'Criar nova Área'
        )
        //Selecionar Viatura
      case 'selecionarViatura':
        return(
          'Selecionar Viatura'
        ) 
      default:
        return false;
    }
  }

  const handleFluxo = () =>{
    switch(fluxo){
      case 'policiais':
        return (
          <FormsNewUser closeModal={()=> setOpen(false)}/>
        )
      case 'fazendas':
        return (
          <FormsNewFazenda closeModal={() => setOpen(false)}/>
        )
      case 'viatura':
        return (
          <FormsNewViatura  closeModal={() => setOpen(false)}/>
        )
      case 'proprietario':
        return (
          <FormsNewProprietario closeModal={() => setOpen(false)}/>
        )
      case 'areas':
        return (
          <FormsNewArea/>
        )
        case 'selecionarViatura':
          return(
            <SelectVehicle
              {...props}
            />
          )
      default:
        return false;
    }
  }

  const handleContent = () => {
    if(props.tipo == 'rotas'){
      return (
        <Card 
          className='menuRotas shadow cardCriar' 
          onClick={()=>{
            localStorage.setItem("edit","")
            localStorage.setItem("edit1","")
            history.push('/PoliceRoutes')
          }}>
          <div className='contentMenu'>
            <AiOutlinePlusCircle className='iconCriar'/>
            <p>Criar <br/>Nova Rota</p>
          </div>
        </Card>
      )
    }

    if(props.tipo == 'selecionarViatura'){
      return (
        <button 
          onClick={()=>{
            setOpen(true)
            setFluxo(props.tipo)
          }}
          className='button'
          >
          Enviar viatura
        </button>
      )
    }

    return(
      <Card className='menuRotas shadow cardCriar' 
      onClick={()=>{
        setOpen(true)
        setFluxo(props.tipo)
    }}>
        <div className='contentMenu'>
            {props.tipo == 'fazendas'
                &&
                <>
                    <AiOutlinePlusCircle className='iconCriar'/>
                    <p>Criar <br/>Nova Fazenda</p>
                </>
            }
            {props.tipo == 'visita'
                &&
                <>
                    <AiOutlinePlusCircle className='iconCriar'/>
                    <p>Criar <br/>Nova visita</p>
                </>
            }
            
            {props.tipo == 'policiais'
                &&
                <>
                    <AiOutlinePlusCircle className='iconCriar'/>
                    <p>Novo Policial</p>
                </>
            }
            {props.tipo == 'proprietario'
              &&
              <>
                  <AiOutlinePlusCircle className='iconCriar'/>
                  <p>Criar <br/>Novo Proprietário</p>

              </>
            }
            {props.tipo == 'areas'
                &&
                <>
                    <AiOutlinePlusCircle className='iconCriar'/>
                    <p>Criar <br/>Nova Área</p>

                </>
            }
            {props.tipo == 'viatura'
                &&
                <>
                    <AiOutlinePlusCircle className='iconCriar'/>
                    <p>Criar <br/>Nova Viatura</p>

                </>
            }
        </div>
    </Card>

    )
  }
  
  return (
    <>
      {handleContent()}
      <Modal
        show={open}
        onHide={() => setOpen(false)}
        aria-labelledby="example-modal-sizes-title-lg"
        contentClassName='dialogModal'
      >
        <Modal.Header>
          <Modal.Title>
            <h5 style={{fontWeight:'bolder'}}>{handleTitle()}</h5>
          </Modal.Title>
          <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>setOpen(false)} style={{fontSize:25, color:'#868686'}}/></Button>
        </Modal.Header>
        <Modal.Body>{handleFluxo()}</Modal.Body>
      </Modal>
    </>
  );
}

export default CustomButtonCriar;