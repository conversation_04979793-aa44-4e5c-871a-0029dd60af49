import axios from 'axios'
import React,{useEffect,useState} from 'react'
import { URL_API } from '../services'
import { getDistance, convertDistance } from 'geolib';

function DistanciaViatura(props) {

    const [distancia, setDistancia] = useState(1000000)
    useEffect(()=>{
        axios.get(URL_API+'panico_last')
        .then(res=>{
            for (let i = 0; i < res.data.length; i++) {
                const distance = getDistance(
                    { latitude: props.lat, longitude: props.lng },
                    { latitude: res.data[i].latitude, longitude: res.data[i].longitude },
                );
                if(distance<distancia){
                    const distanceKM = convertDistance(distance, 'km')
                    setDistancia(distanceKM)
                    window.localStorage.setItem('@ViaturaProx',res.data[0].id_viat)
                    window.localStorage.setItem('@Distancia', distanceKM)
                }
                console.log(distance)
            }
        })
        .catch(res=>{
            console.log(res)
        })
    },[])
    
    return (
        
        <center><div className="nome-fazenda">{distancia}Km</div></center>
        
    )
}

export default DistanciaViatura
