import React, { useState, useContext,  useEffect } from 'react';
import Swal from 'sweetalert2'

const MapContext = React.createContext();

export function useMapContext() {
  return useContext(MapContext);
}

export function MapProvider({children}) {
  //const [raio, setRaio] = useState(0);
  const [idArea, setIdArea] = useState(24);

    // useEffect(()=>{
    //   if ("geolocation" in navigator) {
    //     navigator.geolocation.getCurrentPosition(function(position) {
    //         setLatitude(position.coords.latitude);
    //         setLongitude(position.coords.longitude);
    //         window.localStorage.setItem('latitude', position.coords.latitude)
    //         window.localStorage.setItem('longitude', position.coords.longitude)
    //         console.log(latitude)
    //         console.log(longitude)
    //     });
    //   } 
    //   else  
    //   {
    //       alert("I'm sorry, but geolocation services are not supported by your browser.");
    //   }
    // },[latitude, longitude])

    return (
        <MapContext.Provider value={{idArea, setIdArea}}>
          {children}
        </MapContext.Provider>
      );
}
