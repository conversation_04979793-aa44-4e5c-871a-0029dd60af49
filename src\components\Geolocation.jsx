import React from "react";
import { geolocated } from "react-geolocated";
import Swal from 'sweetalert2'

class Geolocation extends React.Component {


    handleSave() {

      const save = {
        latitude: this.props.coords.latitude,
        longitude: this.props.coords.longitude
      }

      fetch('http://autonomousaudit.servehttp.com:5000/app_aiba', {
        method: 'POST',
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(save)
      }).then(res => {
        Swal.fire(`Enviado com sucesso!`)
      }).catch(err => {
        Swal.fire(`Erro: ${err}`)
      })
    }


    render() {
        return !this.props.isGeolocationAvailable ? (
            <div>Seu navegador não suporta geolocalização</div>
        ) : !this.props.isGeolocationEnabled ? (
            <div>Geolocalização não foi permmitida</div>
        ) : this.props.coords ? (
            <center>
                <div>
                  <p>Latitude: {this.props.coords.latitude}</p>
                  <p>Longitude: {this.props.coords.longitude}</p>
                </div>
                <div>
                  <button onClick={() => this.handleSave()}>Salvar coordenadas</button>
                </div>
            </center>
        ) : (
            <div>Aguardando liberação para uso de sua localização...</div>
        );
    }
}

export default geolocated({
    positionOptions: {
        enableHighAccuracy: false,
    },
    userDecisionTimeout: 5000,
})(Geolocation);