import React, { useState, useEffect, useContext } from 'react';
import { useUserContext } from '../context/authContext';
import { Button } from 'react-bootstrap'
import {usePositionContext} from '../context/locationContext';
import axios from 'axios';
import { SocketContext } from '../context/socketContext';
import Swal from 'sweetalert2'
import {URL_SOCKET} from '../services'

function BotaoPanico() {

  const { userId } = useUserContext();
  const {latitude, longitude} = usePositionContext();
  const socket = useContext(SocketContext);

  // Id do Alerta que estou emitindo
  const [idAlert, setIdAlert] = useState(0);

  //verifiva se o alerta que enviei está em andamento
  const [andamento, setAndamento] = useState(false);

  //verifiva se o Botão de Alerta foi está ativo
  const [ativo, setAtivo] = useState(true);

  // quando o botão de panico é pressionado
  const handlePanicoPress = () =>{
    axios.post(URL_SOCKET+'panico',{
        latitude: latitude,
        longitude: longitude,
        hora: new Date(),
        user_id: window.localStorage.getItem('@Id'),
        tipo: '0'
    }).then(res=>{
      setIdAlert(res.data.last_id); //Pega o id do Alerta
      setAtivo(false); //Seta o 'Cancelar Panico' 
    })
  }

  // quando o botão de cancelar alerta é pressionado
  const handleCancelarPanicoPress = () =>{
      axios.post(URL_SOCKET+'panico_cancelada', {
          id: idAlert
        }).then(res => {
          Swal.fire(`Chamado Cancelado!`)})
      setAtivo(true);
    }    

  // verifica se o meu alerta está em andamento
  useEffect(()=>{
    socket.on('Atenção viatura a caminho', (data) => { 
      console.log(data.id_alerta)
      if(data.id_alerta == idAlert){
        setAndamento(true);
      }
    });
    socket.on('Atenção ocorrencia finalizada', (data) => { 
      console.log(data)
    });
  },[idAlert]);


  return (
    <>
      {ativo
        ? (<Button className={'btn-alerta'} onClick={handlePanicoPress}>Panico</Button>)
        : (<Button className={'btn-alerta'} onClick={handleCancelarPanicoPress} disabled={andamento}> Cancelar Panico</Button>)
      }
      
    </>
  );
}

export default BotaoPanico;