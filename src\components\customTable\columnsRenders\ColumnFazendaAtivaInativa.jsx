import React from 'react';
import ReactTooltip from 'react-tooltip';
import styled from 'styled-components'
import './ColumnStatusRender.css';



const ActiveOrNot = styled.div`
   width: 10px;
   height: 10px;
   border-radius: 3px;
   background-color: ${({ativo}) => ativo == "INATIVO" ? "#ff0000ba" : "rgba(0, 169, 86, 1)"};
`

const ColumnFazendaAtivaInativa = (name, column) =>{
    
    return {
        name: name, //header do datatable   
            cell: (row,index) => { 
                                                                            
                return ( 
                    <>
                        <ActiveOrNot ativo={row[column]}  data-tip data-for={`tooltip-${index}`} className={`column-status`} />
                        <ReactTooltip place="bottom" className="text" id={`tooltip-${index}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{row[column]}</span>         
                        </ReactTooltip> 
                    </>
                )
            }
    }
}

export default ColumnFazendaAtivaInativa; 