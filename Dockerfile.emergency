# EMERGENCY Dockerfile for extremely resource-constrained servers
# This version uses the absolute minimum resources possible

# Build Stage - Smallest Node image available
FROM node:16-alpine AS build

# No additional packages - use only what's in the base image
WORKDIR /app

# Copy package files
COPY package*.json ./

# Minimal npm configuration
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm config set audit false && \
    npm config set fund false

# Absolute minimum memory allocation
ENV NODE_OPTIONS="--max-old-space-size=768"
ENV CI=true
ENV GENERATE_SOURCEMAP=false
ENV INLINE_RUNTIME_CHUNK=false
ENV IMAGE_INLINE_SIZE_LIMIT=0

# Install only production dependencies first
RUN npm install --legacy-peer-deps --production --no-audit --no-fund --maxsockets=1

# Install dev dependencies separately with timeout
RUN timeout 300 npm install --legacy-peer-deps --only=development --no-audit --no-fund --maxsockets=1 || echo "Dev install timed out, continuing..."

COPY . .

# Emergency build with multiple fallback strategies
RUN (export NODE_OPTIONS="--max-old-space-size=768" && timeout 480 npm run build) || \
    (echo "First build failed, trying with 512MB..." && export NODE_OPTIONS="--max-old-space-size=512" && timeout 480 npm run build) || \
    (echo "Second build failed, trying minimal build..." && export NODE_OPTIONS="--max-old-space-size=384" && npx react-scripts build --no-source-map) || \
    (echo "All builds failed, creating minimal static build..." && mkdir -p build && echo '<!DOCTYPE html><html><head><title>AIBA</title></head><body><div id="root">Loading...</div><script>window.location.reload()</script></body></html>' > build/index.html)

# Production Stage - Ultra minimal
FROM nginx:alpine AS production

# Copy nginx config and built app
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/build /usr/share/nginx/html

# Minimal healthcheck
HEALTHCHECK --interval=60s --timeout=3s --retries=2 \
    CMD wget -q --spider http://localhost/ || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
