import React, {useState, useEffect} from 'react'
import RoutesMap from '../../../components/RoutesMap'
import RoutesList from './RoutesList'
import {URL} from '../../../services/index'
import { MapListTemplate } from '../../components/templates'
import axios from 'axios'

function Routes() {

    const [routes, setRoutes] = useState([]);
    const [areaSelected, setAreaSelected] = useState();

    const getRoutes = () => {
        axios.get(`${(areaSelected !== 'todos' && areaSelected !== undefined) ? `${URL}todas_rotas?areaId=${Number(areaSelected)+1}` : `${URL}todas_rotas`}`)
        .then(res => setRoutes(res.data.reverse()))
        .catch( res => console.log(res))
    }

    useEffect(() =>{
        getRoutes();
    },[areaSelected])

    return (
        <>
            <MapListTemplate
                map={<RoutesMap setAreaSelected={(e) => setAreaSelected(e)}/>}
                list={<RoutesList areaSelected={areaSelected} routes={routes} getRoutes={getRoutes}/>}
            />
        </>
    )
}

export default Routes;
