import React from 'react';
import './ColumnButtonViaturaRender.css';
import '../../SelectVehicle';
import CustomButtonCriar from '../../CustomButtonCriar';

const ColumnButtonViaturaRender = (name,title,description,refresh) =>{

    return {
        name: name, //header do datatable   
            cell: (row) => {
                if(row.status === 'Aguardando atendimento'){
                    return ( 
                        <div>
                            <CustomButtonCriar
                                tipo="selecionarViatura"
                                idOcorrencia={row.id}
                                fetchOcorrencias={refresh}
                            />
                        </div>                           
                    )
                } 
                else if(row.status === 'Em atendimento'){
                    return(
                        <>
                            <div className="Description">#{row[title]}</div>
                            <div className="Description">{row[description]}</div>
                        </>
                    ) 
                       
                } 
                else if(row.status === 'Cancelada'){
                    return 'Cancelada';
                }
                else if(row.status === 'Concluída'){
                    return `#${row[title]}`;
                }
            }
    }
}

export default ColumnButtonViaturaRender; 