import React, {useEffect} from 'react';
import Map from '../../../components/Mapa';

import { Row, Col, Card } from 'react-bootstrap'

import { URL } from '../../../services';
import CustomButton from '../../../components/CustomButton';
import CustomButtonCriar from '../../../components/CustomButtonCriar'
import { useState } from 'react';

import axios from 'axios'
import { MapListTemplate } from '../../components/templates';
import FarmsList from './FarmsList';

function Fazendas() {

  const [areasAtuacao, setAreasAtuacao] = useState([]);

  const ident = window.localStorage.getItem('@Ident')

  const getAreas = () => {
    axios.get(`${URL}todas_areas`)
    .then(res=> setAreasAtuacao(res.data))
    .catch(res => console.log(res))
  }

  useEffect(() => {
    getAreas();
  },[])

  return (
    <>
    <MapListTemplate
      map={
        <Col md={4}>
          <Card className='cardMap shadow' id='cardMap' style={{width:'420px'}}>
            <h5 className='mt-4 px-3 mb-3 text-dark'>Resumo por Área</h5>
            <Map height={650} largura={"200"} areasAtuacao={areasAtuacao}/>
            <div className='mx-auto menu'>
              {ident == 'ADMINAIBA'
                ?
                <Row >
                  <Col md='6' sm='6' lg='6'>
                    <CustomButtonCriar tipo='fazendas'/>
                  </Col>
                </Row>
                :
                <>
                <Row >
                  <Col md='6' sm='6' lg='6'>
                    <CustomButton tipo='viaturas'/>
                  </Col>
                  <Col md='6' sm='6' lg='6'>
                    <CustomButton tipo='fazendas'/>
                  </Col>
                </Row>
                <Row>
                  <Col md='6' sm='6' lg='6'>
                    <CustomButton tipo='rotas'/>
                  </Col>
                  <Col md='6' sm='6' lg='6'>
                    <CustomButton tipo='policiais'/>
                  </Col>
                </Row>
                </>
              }
            </div>
          </Card>          
        </Col>
      }
      list={
        <FarmsList areas={areasAtuacao}/>
      }
    />
    </>
  );
}

export default Fazendas;