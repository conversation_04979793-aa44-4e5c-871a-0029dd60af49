import * as React from 'react'
import { render,screen } from '@testing-library/react';
import AppTest from './AppTest';
import Map from '../components/Mapa';
import ReactTooltip from 'react-tooltip';
import { GiHouse } from 'react-icons/gi';
import MapGL from "react-map-gl";


describe('Verificando  Map', () => {
  it('Reinderize o Map, toolTip', () => {
    render(<AppTest><Map/></AppTest>);

    const {container} = render(<ReactTooltip/>)

    expect(container).toBeTruthy();
  })

  it('Reinderize o Map, Icone GiHouse', () => {
    render(<AppTest><Map/></AppTest>);

    const {container} = render(<GiHouse/>)

    expect(container).toBeTruthy();
  })
  
  it('Verificando o Source e Layer', () => {
    render(<AppTest><Map/></AppTest>);

     const mapGl = render(<MapGL/>)

    expect(mapGl).toBeTruthy();
  })
  
})