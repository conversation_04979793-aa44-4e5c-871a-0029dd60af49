import React, {useContext, useEffect} from 'react';
import Footer from '../components/Footer'
import Map from '../components/Mapa';
import Header from '../components/Header';

import { Container, Row, Col, Card, Spinner } from 'react-bootstrap'


function BasicMap({areaRota}) {
    return (
      <Card className='cardMap shadow' style={{height:'650px',width:'420px'}} id='cardMap'>
        {/* <Card.Header className='cardMapHeader'>
          Resumo por Área
        </Card.Header> */}
        <h5 className='mt-4 px-3 mb-3 text-dark'>Área de atuação selecionada para a rota</h5>
            <Map areaRota={areaRota} height={650} largura={"200"}/>
      </Card>
    )
}
 
      

export default BasicMap

