import React from 'react';
import Swal from 'sweetalert2'
import axios from 'axios'

import './ColumnTitleDescriptionRender.css';
import { NavDropdown, Row, Col } from 'react-bootstrap';
import ReorderIcon from '@material-ui/icons/Reorder';
import ArrowForwardIosIcon from '@material-ui/icons/ArrowForwardIos';
import { URL_API } from '../../../services';
import { useHistory } from 'react-router';



const ColumnNavDropdownRender = (id, openEditModal, update) =>{ 
  
  const history = useHistory();

  const handleClickEdit = row =>{
    openEditModal(row)
  }

  const ident = window.localStorage.getItem('@Ident')
  const handleClickDelete = row => {
    if(row.ativo_inativo === "INATIVO") {
      Swal.fire({
        title:'Ativar fazenda',
        text:'Deseja ativar esta fazenda ?',
        icon:'question',
        showCancelButton: true,
        cancelButtonText: 'Não',
        confirmButtonText: `Sim`,
      }).then(async (result) => {
        if (result.isConfirmed) {
          const dataToSend = {...row, ativo_inativo: "ATIVO"}
      
          const response = await axios.put(URL_API+"atualizar_fazenda", dataToSend)
          console.log("Response:", response)
          if(response.data === "OK") {
            update()
          }
        }
      })
    } else {
      Swal.fire({
        title:'Desativar fazenda',
        text:'Deseja desativar esta fazenda ?',
        icon:'question',
        showCancelButton: true,
        cancelButtonText: 'Não',
        confirmButtonText: `Sim`,
      }).then(async (result) => {
        if (result.isConfirmed) {
          const dataToSend = {...row, ativo_inativo: "INATIVO"}
      
          const response = await axios.put(URL_API+"atualizar_fazenda", dataToSend)
          console.log("Response:", response)
          if(response.data === "OK") {
            update()
          }
        }
      })
    }
  }
  const handleClick = (idFazenda) =>{
    window.localStorage.setItem('@IdFazenda', Number(idFazenda.id_fzd))
    console.log(idFazenda.id_fzd)
    history.push('/detalhefazenda')
  }
  return {         
      id: id,
      cell: (row) => {  
          return (    
            <>        
              <NavDropdown title={<ReorderIcon color="disabled"/>} id="basic-nav-dropdown">                         
                <NavDropdown.Item >Ações</NavDropdown.Item>

                <NavDropdown.Divider />
                {ident == "ADMINAIBA" ? 
                  <NavDropdown.Item  onClick={() => handleClickEdit(row)}>
                    <ArrowForwardIosIcon style={{ fontSize: '1rem', marginRight: '10px' }} />
                    Editar
                    </NavDropdown.Item>
                    :
                    <></>
                }
                

                <NavDropdown.Item  onClick={() => handleClick(row)}>
                  <ArrowForwardIosIcon style={{ fontSize: '1rem', marginRight: '10px' }} />
                  Detalhes da Fazenda
                </NavDropdown.Item>
                {ident == "ADMINAIBA" ? 
                  <NavDropdown.Item  onClick={() => handleClickDelete(row)}>
                    <ArrowForwardIosIcon style={{ fontSize: '1rem', marginRight: '10px' }} />
                    {row.ativo_inativo == "ATIVO" ? "Desativar" : "Ativar"}
                  </NavDropdown.Item>
                  :
                  <></>
                }
                

              </NavDropdown> 
            </>                     
          )
      }
  }
}

export default ColumnNavDropdownRender; 
