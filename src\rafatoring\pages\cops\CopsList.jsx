import React, {useState,useEffect} from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './CopsSingle'
import { Card, Row, Col, Spinner } from 'react-bootstrap';
import FilterRegister from './FilterRegister';
import { URL } from '../../../services/index';
import { useUserContext } from '../../../context/authContext';
import axios from 'axios';
import { TableComponent } from '../../components/organisms';
import { TableBody, TableCell, TableHead, TableRow } from '@mui/material';


function OfficerList() {
    const {toggle,select,search1} = useUserContext();

    const [cops, setCops] = useState([]);
    const [copsAuxiliar, setCopsAuxiliar] = useState([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };
    
    const change=(a)=>{
        if(a.ativo_inativo==="ATIVO")return -1
        else{
            return 0
        }     
    }

    async function getCops(){
        axios.get(`${URL}todos_policiais`)
        .then(res => {
            let data = res.data.sort(change);
            setCops(data);
            setCopsAuxiliar(data);
        })
        .catch(err => console.log(err));
    }

    useEffect(() => {
        getCops();
    }, [toggle])

    console.log(search1)

    useEffect(() => {
        if(cops){   
            setCops(copsAuxiliar);
            if(select === "Coordenador"){
                setCops(state=>state.filter(el => el.coordenador==="1" && el.nome.includes(search1)))
            }
            else if(select==="Policial"){
                setCops(state=>state.filter(el => el.coordenador!=="1" && el.nome.includes(search1)))
            }
            else{
                if(search1.length < 3){
                    getCops();
                }
                else{
                    setCops(state=>state.filter(el => el.nome.includes(search1)))
                }
            }
            
        }
    }, [select, search1])
    


    return (
        <>
            {cops
                ?
                <TableComponent
                    count={cops.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Usuários Cadastrados'
                    subtitle={<FilterRegister/>}
                >
                    <TableHead>
                        <TableRow>
                            <TableCell></TableCell>
                            <TableCell align='center'>Nome/Email</TableCell>
                            <TableCell align='center'>Cargo</TableCell>
                            <TableCell align='center'>Status</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {cops.length == 0 
                            ?
                            <p style={{padding:'10px'}}>Nenhum dado encontrado</p>
                            :
                            <CopsSingle cops={cops} page={page} rowsPerPage={rowsPerPage}/>
                        }
                    </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>
    )
}

export default OfficerList
