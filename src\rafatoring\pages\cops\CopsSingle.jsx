import React,{useState,useEffect} from 'react'
import MenuIcon from '@material-ui/icons/Menu';
import axios from 'axios';
import { <PERSON>, Row, Col, Spin<PERSON>,<PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { Menu, MenuItem } from '@material-ui/core';
import { IoClose } from "react-icons/io5"
import { useUserContext } from '../../../context/authContext';
import { URL } from '../../../services';
import ReactTooltip from 'react-tooltip';
import { Avatar, TableRow, TableCell, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../components/organisms';
import { BsFillPersonCheckFill } from "react-icons/bs";
import { AiOutlineUserDelete } from "react-icons/ai";
import { FaRegEdit } from "react-icons/fa";
import PoliceDefaultImg from '../../../assets/img/avatar.jpg';
import EditPolice from './EditPolice';

function CopsSingle({cops, page, rowsPerPage}) {

    const {toggle,settoggle} = useUserContext();

    const [openModal,setOpenModal] = useState(false);
    const [anchor, setanchor] = useState(0);
    const [copEditar, setCopEditar] = useState({});
   
    function submit(data){
        setanchor(null)
        axios.put(URL+'atualizar_policial',{

            "matricula": data.matricula,
            "senha": data.senha,
            "posto_graduacao": data.posto_graduacao,
            "nome": data.nome,
            "foto": data.foto,
            "unidade": data.unidade,
            "email": data.email,
            "telefone": data.telefone,
            "coordenador": data.coordenador,
            "ativo_inativo": data.ativo_inativo==="ATIVO"?"INATIVO":"ATIVO",
            "id": data.id
                
        })
        .then(res=>{
            settoggle(!toggle)
        })
        .catch(err=>{
            console.log(err)
        })
    }

    const handleDelete=()=>{
        setanchor(null)
    }

    const handleEdit = (data) => {
        setanchor(null);
        setOpenModal(true);
        setCopEditar(data);
    }

    const showModal=()=>{
        return (<EditPolice data={copEditar} openModal={[openModal, setOpenModal]}/>)
    }

    
    return (
        <>
        {cops?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index) =>(
            <TableRow key={index}>
                <TableCell><Avatar alt={i.nome} src={i.foto || PoliceDefaultImg}/></TableCell>
                <TableCell align='center'>
                    <h6 style={{fontWeight:'bold'}}>{i.nome}</h6>
                    <h6>{i.email}</h6>
                </TableCell>
                <TableCell align='center'>
                    <h6>{i.coordenador == 0 ? 'Policial' : 'Coordenador'}</h6>
                </TableCell>
                <TableCell align='center'>
                    <ReactTooltip place="bottom" className="text" id={`tooltip-${i.id}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{i.ativo_inativo === "ATIVO" ? "ATIVO" : "INATIVO"}</span>          
                    </ReactTooltip>
                    <div 
                        data-tip 
                        data-for={`tooltip-${i.id}`}
                        style={{
                            width:'10px',
                            height:'10px', 
                            backgroundColor:i.ativo_inativo==="ATIVO"?'rgba(0, 169, 86, 1)':"#ff0000ba", 
                            borderRadius:'3px',
                            marginLeft: '15px'
                        }}>
                    </div>
                </TableCell>
                <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem onClick={() => handleEdit(i)}>
                            <ListItemIcon><FaRegEdit size={20}/></ListItemIcon>
                            Editar
                        </MenuItem>
                        {i.ativo_inativo === "ATIVO" 
                            ?
                                <MenuItem onClick={() => submit(i)}>
                                    <ListItemIcon><AiOutlineUserDelete size={20}/></ListItemIcon>
                                    Desativar
                                </MenuItem>
                            : 
                                <MenuItem onClick={() => submit(i)}>
                                    <ListItemIcon><BsFillPersonCheckFill size={20}/></ListItemIcon>
                                    Ativar
                                </MenuItem>
                        }
                    </TableItemMenuMulti>
                </TableCell>
            </TableRow>
        ))}
        <Modal
            show={openModal}
            onHide={() => setOpenModal(false)}
            aria-labelledby="example-modal-sizes-title-lg"
            contentClassName='dialogModal'
        >
            
            <Modal.Header>
                <Modal.Title>
                <h5 style={{fontWeight:'bolder'}}>Editar Usuário</h5>
                </Modal.Title>
                <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>setOpenModal(false)} style={{fontSize:25, color:'#868686'}}/></Button>
            </Modal.Header>
            <Modal.Body>
                {showModal()}
            </Modal.Body>
        
        </Modal>
        </>
    )
}

export default CopsSingle;
