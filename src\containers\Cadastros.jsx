import React, {useContext, useEffect} from 'react';
import Footer from '../components/Footer'
import Map from '../components/Mapa';
import Header from '../components/Header';

import { Container,Row, Col, Card, Spinner } from 'react-bootstrap'
// import Menu from '../components/menuRapido';

import { PREV, URL } from '../services';
import CustomButton from '../components/CustomButton';
import CustomButtonCriar from '../components/CustomButtonCriar'
// import CustomTable from './FarmsList';
import ColumnTitleDescriptionRender from '../components/customTable/columnsRenders/ColumnTitleDescriptionRender';
import ColumnNavDropdownRender from '../components/customTable/columnsRenders/ColumnNavDropdownRender'
import { useState } from 'react';
import ColumnStatusRender from '../components/customTable/columnsRenders/ColumnStatusRender';

import axios from 'axios'

const data = [
  { 
    numberCar:'#4843', 
    components:'11componentes',
    region: 'Caieras',
    latLng: '-229334-2; -229330-2',
    name: '<PERSON><PERSON>ria de Viver',
    owner: '<PERSON> lima soares',
    date: '22/05/2021',
    time: '12:33:42', 
    status: '1'
  },
  {  
    numberCar:'#4843', 
    components:'11componentes', 
    region: 'Caieras',
    latLng: '-229334-2; -229330-2',
    name: 'Alegria de Viver',
    owner: 'Rodrigo lima soares',
    date: '22/05/2021',
    time: '12:33:42',
    status: '2'
  },
  {  
    numberCar:'#4843', 
    components:'11componentes', 
    region: 'Caieras',
    latLng: '-229334-2; -229330-2',
    name: 'Alegria de Viver',
    owner: 'Rodrigo lima soares',
    date: '22/05/2021',
    time: '12:33:42',
    status: '3'
    
  },
  {
    numberCar:'#4843', 
    components:'11componentes',
    region: 'Caieras',
    latLng: '-229334-2; -229330-2',
    name: 'Alegria de Viver',
    owner: 'Rodrigo lima soares',
    date: '22/05/2021',
    time: '12:33:42', 
    status: '1'
  },
]

function Cadastros() {

  const[largura, setLargura] = useState();
  const [fazendas, setFazendas] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(()=>{
    setLoading(true);
    axios.get(URL+'selecionar_todos_proprietarios')
    .then(res=>{
        setLoading(false)
        //for (let i = 0; i < res.data.length; i++) {
            setFazendas(res.data)
            // setOptions(draft=>{
            //     draft.push(res.data[i][1])
            // })
        //}
    })
    .catch(err=>console.log(err))
  },[])
  useEffect(() => {
    setLargura(document.getElementById('cardMap').clientWidth);
  }, [largura])
  
  return (
    <>
     <Header />

      <Container className="mt-5">   
        <Row>
          <Col md={4}>
            <Card className='cardMap shadow' id='cardMap'>
              {/* <Card.Header className='cardMapHeader'>
                Resumo por Área
              </Card.Header> */}
              <h5 className='mt-4 px-3 mb-3 text-dark'>Resumo por Área</h5>
              <Map largura={largura}/>
              <div className='mx-auto menu'>
                {PREV == 'aiba'
                  ?
                  <Row >
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='fazendas'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='rotas'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='areas'/>
                    </Col>
                  </Row>
                  :
                  <>
                  <Row >
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='viaturas'/>
                    </Col>
                   
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='proprietario'/>
                    </Col>
                  </Row>
                  <Row>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButton tipo='rotas'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='policiais'/>
                    </Col>
                  </Row>
                  </>
                }
              </div>
            </Card>          
          </Col>
          <Row >
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='viatura'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='proprietario'/>
                    </Col>
                  </Row>
                  <Row>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='rotas'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='policiais'/>
                    </Col>
                  </Row>
        </Row>
      </Container>
      <Footer />
    </>
  );
}

export default Cadastros;