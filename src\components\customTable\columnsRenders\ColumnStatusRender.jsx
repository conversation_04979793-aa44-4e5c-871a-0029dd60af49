import React from 'react';
import './ColumnStatusRender.css';
import ReactTooltip from 'react-tooltip';

const ColumnStatusRender = ( ) =>{ 
  
  let desativado = "desativado"; /* cinza */
  let visitado = "visitado";    /*verde */ 
  let aguardandovisita = "aguardandovisita";  /* azul */ 
  let classe = " ";
  let texto = " ";
 

  return {
    cell:(row, index) => {
      if(row.status === '1') {
        classe = desativado
        texto = "Desativado"  
      }
      else if (row.status === '2') {
        classe = visitado
         texto = "Visitado"
      }
      else if (row.status === '3'){
        classe = aguardandovisita
        texto = "Aguardando visita"
      }
    
      return (          
        <div>                                
           <div data-tip data-for={`tooltip-${index}`} className={`column-status ${classe}`}></div>
            <ReactTooltip place="bottom" className="text" id={`tooltip-${index}`} aria-haspopup='true' type="dark" effect='solid'>            
            <span>{texto}</span>           
          </ReactTooltip>                                    
        </div>
      )
    }
  }
}

export default ColumnStatusRender; 


