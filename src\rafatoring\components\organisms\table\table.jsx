import React from 'react';
import { Paper, TableContainer, Table, TablePagination } from '@mui/material';

const TableComponent = ({children, count, page, rowsPerPage, onPageChange = () =>{}, onRowsPerPageChange = () =>{}, title, subtitle}) =>{
    return(
        <TableContainer component={Paper} style={{borderRadius:'20px'}}>
            <div style={{padding:'10px', display:'flex', justifyContent:'space-between'}}>
                <h5 className='mt-4 px-3 mb-3 text-dark'>{title}</h5>
            
                <span className='mt-4 px-3 mb-3 text-dark'>{subtitle}</span>
            </div>
            <Table>
               {children}
            </Table>
            <TablePagination
                labelDisplayedRows={({from, to, count})=>{
                    return `${from}-${to} de ${count}`
                }}
                labelRowsPerPage='Linhas por página'
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={count}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={onPageChange}
                onRowsPerPageChange={onRowsPerPageChange}
            />
        </TableContainer>
    )
}

export default TableComponent;