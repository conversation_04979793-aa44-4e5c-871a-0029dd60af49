import React, {useEffect, useState} from 'react';
import Footer from '../components/Footer'
import Map from '../components/Mapa';
import Header from '../components/Header'
import CardRotas from '../components/CardRotas';
import { Container,Row, Col, Card, Image, ListGroup, CardGroup } from 'react-bootstrap'
import './DetalheRotas.css';
import axios from 'axios';
import { URL_API } from '../services';
import viaturaImg from '../assets/img/bg-11.jpg'
import avatar from '../assets/img/avatar.jpg'


function DetalheRotas(props) {

  const [largura, setLargura] = useState();
  const [dadosRota, setDadosRota] = useState({});
  const [load, setLoad] = useState(false)

  useEffect(() => {
    setLargura(document.getElementById('cardMap').clientWidth);
  }, [largura])

  const fetchRota = async (id) => { 
    const response = await axios.post(URL_API+"buscar_rota", {id})

    const fazendas = response.data.fazendas.map((fazenda) => {
      return {
        ...fazenda, 
        visitado: 
        response.data.fazendas_visitadas.some((fazendaVisitada) => {
          return parseInt(fazenda.id_fzd) === parseInt(fazendaVisitada.fzd_id)
        })
      }

    })

    setDadosRota({
      ...response.data,
      fazendas,
    })
    setLoad(true);
  }

  const showJustificativa = () => {
    if(dadosRota !== undefined){
      if(dadosRota.rota.justificativa !== null){
        return(
          <Row className="mt-5">
              <h5 className='px-3 mb-3 text-center'>Justificativa</h5>
              <p className='px-3 mb-3 text-center'>{dadosRota.rota.justificativa}</p>
          </Row>
          
        )
      }
    }
    
  }

  useEffect(() => {
    fetchRota(props.match.params.id)
  }, [])

  return (
    <>
      <Header />

      <Container className="mt-5">   
        <Row>
          <Col md={5}>
            <Card className='cardMap shadow' id='cardMap'>
              <Map largura={largura}/>
              <div className='mx-auto menu'>
                <Row>
                    <Col>
                        <Card className='menuInfos shadow'>
                            <div className='contentInfo'>
                                Área de Atuação
                            </div>
                            <div className='areaAtuacao-cidade'>
                                {dadosRota.area_atuacao}
                            </div>
                        </Card>
                    </Col>
                </Row>
              </div>
            </Card>
          </Col>
          <Col md={7}>
            <Row>
              <Col>
                <Card className='menuInfos rotas-cards-iniciais shadow'>
                  <div className='contentInfo'>
                    Data de partida
                    <div className='contentInfoNumber'>
                    { dadosRota.rota ? dadosRota.rota.data_inicio : ""}
                    </div>
                  </div>
                </Card>
              </Col>
              <Col>
                <Card className='menuInfos rotas-cards-iniciais shadow'>
                  <div className='contentInfo'>
                      Fazendas Visitadas
                    <div className='contentAtivNumber'>
                    { dadosRota.fazendas_visitadas ? dadosRota.fazendas.filter(fazenda => fazenda.visitado).length : "" }
                    </div>                           
                  </div>
                </Card>
              </Col>
              <Col>
                <Card className='menuInfos rotas-cards-iniciais shadow'>
                  <div className='contentInfo'>
                    Kms rodados
                    <div className='contentInativNumber'>
                      {dadosRota.rota && !isNaN(dadosRota.rota.kilometragem_inicial) && !isNaN(dadosRota.rota.kilometragem_final)
                      ?
                      `${parseFloat(dadosRota.rota.kilometragem_inicial) - parseFloat(dadosRota.rota.kilometragem_final)} Kms`
                      :                                                                               
                      "Indefinido"
                      }
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
            <Row className="fazendas-rota">
              <Col>
                <Card className='cardRotas shadow'>
                  <Row>
                    <p>Fazendas existentes na Rota</p>
                  </Row>
                    <Row xs={1} md={4} className="g-4">
                        {dadosRota.fazendas !== undefined ? dadosRota.fazendas.map((fazenda) => {
                            return(
                              <CardRotas
                                {...fazenda} 
                             />
                            )
                        }): <div></div>}              
                    </Row>
                </Card> 
              </Col>    
            </Row>
          </Col>
        </Row>
      </Container>

      <Container className="mt-5">
          <Row>
              <Col md={5}>
                <h5 className='px-3 mb-3 text-center'>Viatura da Rota</h5>
                {dadosRota.viatura ? dadosRota.viatura.map((viat) => {
                  return (
                  <Card className='shadow text-center-viatura-da-rota'>
                      <Row className="row-viatura-por-rota">
                          <Col style={{marginLeft: '5%'}}>
                          <Image src={viat.imagem_viat ? viat.imagem_viat : viaturaImg} fluid />
                          </Col>
                          <Col style={{marginRight: '5%'}}>
                                <Card.Title>
                                  Viatura da Rota<br/>
                                  {viat.placa}
                              </Card.Title> 
                              <Card.Text>{viat.modelo_viat }</Card.Text> 
                          </Col>
                      </Row>
                      <hr  style={{
                          height: .5,
                          marginRight: '10%',
                          marginLeft: '10%',
                          borderColor : '#000000'
                      }}/>
                      <Row className="text-center">
                          <Col>
                              <span>Km ao iniciar rota:</span>
                              <p>{viat.km_inicial}</p>
                          </Col>
                          <Col>
                              {/* <span>Visitas realizadas:</span> */}
                              
                          </Col>
                      </Row>
                  </Card>
                )}) : ""}

              </Col>
              <Col md={7}>

                <h5 className='px-3 mb-3 text-center'>Componentes da rota</h5>
                <Row xs={1} md={2} className="g-4" >
                    {dadosRota.componentes_policiais !== undefined ? dadosRota.componentes_policiais.map((componente) => {
                        return(
                          <Col>
                            <Card className="card-componente shadow ">
                              {componente.responsavel === true && 
                                <span 
                                className="responsavel"> 
                                  Responsável                              
                              </span>
                              }
                                <Row className="componentes-da-rota">
                                    <Col style={{marginLeft: '5%'}}>
                                        <Image className="img-componente" src={componente.foto ? componente.foto : avatar} fluid />
                                    </Col>
                                    <Col style={{marginRight: '5%'}}>
                                        <Card.Title className="nome-componente">                                       
                                            {componente.nome}
                                        </Card.Title>
                                        <Card.Text>{componente.pm_patente}</Card.Text>
                                    </Col>
                                </Row>
                            </Card>
                          </Col>
                        ) 
                    }) : <div></div>}
                </Row>
              </Col>
          </Row>
          {load && showJustificativa()}
          
      </Container>

      <Footer />
    </>
  );
}

export default DetalheRotas;