import React ,{useState} from 'react'
import {Container, Card, Row, Col, Spinner } from 'react-bootstrap';
import FilterListIcon from '@material-ui/icons/FilterList';
import AddCircleOutlineOutlinedIcon from '@material-ui/icons/AddCircleOutlineOutlined';
import SearchBar from 'material-ui-search-bar'


function FilterProperty(props) {

    const [search, setsearch] = useState({value:""})
    return (
        <>
            <div style={{height:"50px",display:'flex',maxWidth:'1250px',justifyContent:'space-between',alignItems:'center'}} >    
                <span>{props.text}</span>
                {/* <span style={{display:'flex',alignItems:'center'}}>
                    {props.show==="1"?<AddCircleOutlineOutlinedIcon style={{marginRight:'20px'}}/>:null}
                    <FilterListIcon style={{marginRight:'20px'}}/>
                    <SearchBar value={search.value} onCancelSearch={()=>{setsearch({value:''})}} onRequestSearch={() => window.location="#"} onChange={(value)=>setsearch({value:value})}
                        style={{
                            margin: '0 auto',
                            height:'30px',
                            backgroundColor:'#F2F2F2',
                            maxWidth: 200,
                            borderRadius:'20px'
                        }}
                    />
                </span> */}
            </div>
        </>
    )
}

export default FilterProperty
