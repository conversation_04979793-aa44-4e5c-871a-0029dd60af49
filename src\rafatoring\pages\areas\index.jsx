import React, {useContext, useEffect, useState} from 'react';
import Map from '../../../components/Mapa';
import CustomButtonCriar from '../../../components/CustomButtonCriar';
import CustomButton from '../../../components/CustomButton';

import { URL_API } from '../../../services';

import axios from 'axios';

import { Row, Col, Card } from 'react-bootstrap'
import { MapListTemplate } from '../../components/templates';
import AreasList from './areasList';

function Areas() {

  const [largura, setLargura] = useState();

  const [areasAtuacao, setAreasAtuacao] = useState([]);

  useEffect(() => {
    setLargura(document.getElementById('cardMap').clientWidth);
  }, [largura])
  
  async function fetchData(){
      const areasResponse = await axios.get(URL_API+"todas_areas")
      setAreasAtuacao(areasResponse.data)
  }
  useEffect(()=>{
    fetchData();
  },[])

  return (
    <>
      <MapListTemplate
        map={
          <Card className='cardMap shadow' id='cardMap'>
              {/* <Card.Header className='cardMapHeader'>
                Resumo por Área
              </Card.Header> */}
              <h5 className='mt-4 px-3 mb-3'>Áreas de Atuação no Mapa</h5>
              <Map 
                  largura={largura}
                  areasAtuacao={areasAtuacao}
                />
              <div className='mx-auto menu'>
                <Row >
                    <Col md='6' sm='6' lg='6'>
                      <CustomButtonCriar tipo='areas'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButton tipo='fazendas'/>
                    </Col>
                </Row>
                <Row >
                    <Col md='6' sm='6' lg='6'>
                      <CustomButton tipo='rotas'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                      <CustomButton tipo='policiais'/>
                    </Col>
                </Row>
              </div>
            </Card>
        }
        list={<AreasList/>}
      />
    </>
  );
}

export default Areas;
