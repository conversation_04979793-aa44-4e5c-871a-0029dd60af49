import React, {useEffect, useState} from 'react';
import Footer from '../../../../components/Footer'
import Map from '../../../../components/Mapa';
import Header from '../../../../components/Header'


import { Container,Row, Col, Card } from 'react-bootstrap'
import QrCode from '../../../../components/QrCode';
import axios from 'axios';
import { URL } from '../../../../services';
import styled from 'styled-components'
import { useParams } from 'react-router-dom';
import FarmsDetailsList from './farmsDetailsList';


const DetailDiv = styled.div`
  padding: 8px 32px;
  span {
    font-size: 13px;
    color: #97ADB6;
  }
  p {
    font-size: 15px;
    color: #3E4958;
  }
  hr {
    margin: 0;
  }
`


function DetalheFazendas() {

  const {id} = useParams();
  const [largura, setLargura] = useState();
  const [dadosFazenda, setDadosFazenda] = useState({});
  const [visitasFazenda, setVisitasFazenda] = useState([]);

  useEffect(() => {
    setLargura(document.getElementById('cardMap').clientWidth);
  }, [largura]);

  useEffect(() => {

    axios.post(URL+'buscar_fazenda',{
      id_fzd: id
    })
    .then(res=>{
      setDadosFazenda(res.data)
      console.log("buscar_fazenda:",res.data)
    })
    .catch(err=>{
      console.log(err)
    })

    axios.post(URL+'detalhe_visita',{
      id: id
    }).then(res=>{
      setVisitasFazenda(res.data)
    })
    .catch(err=>{
      console.log(err)
    })


  }, [])

  return (
    <>
      <Header />

      <Container className="mt-5">   
        <Row>
          <Col md={5}>
            <Card className='cardMap shadow' id='cardMap'>
              <h5 className='mt-4 px-3 mb-3 text-dark'>Resumo por Área</h5>
              <Map largura={largura}/>
              <DetailDiv>
                <span>Nome da fazenda</span>
                <p>{dadosFazenda.nome_da_fazenda}</p>
                <hr />
              </DetailDiv>
              <DetailDiv>
                <span>Proprietário</span>
                <p>{dadosFazenda.nome_do_proprietario}</p>
                <hr />
              </DetailDiv>
              <DetailDiv>
                <span>Área</span>
                <p>{dadosFazenda.area_de_operacao}</p>
                <hr />
              </DetailDiv>
              <DetailDiv>
                <Row>
                  <Col>
                    <span>Latitude</span>
                    <p>{dadosFazenda.latitude}</p>
                  </Col>
                  <Col>
                    <span>Longitude</span>
                    <p>{dadosFazenda.longitude}</p>
                  </Col>
                </Row>
                <hr />
              </DetailDiv>
              <DetailDiv>
                <Row>
                  <Col>
                    <span>Bairro</span>
                    <p>{dadosFazenda.municipio}</p>
                  </Col>
                  <Col>
                    <span>Município</span>
                    <p>{dadosFazenda.municipio}</p>
                  </Col>
                </Row>
              </DetailDiv>
            </Card>
            <QrCode nomeFazenda={dadosFazenda.nome_da_fazenda} id={id}/>
          </Col>
          <Col md={7}>
            <FarmsDetailsList visits={visitasFazenda}/>
          </Col>
        </Row>
      </Container>

      <Footer />
    </>
  );
}

export default DetalheFazendas;