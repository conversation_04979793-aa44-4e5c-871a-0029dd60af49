body {
  margin: 0;
  font-family: 'Montserrat-Regular', 'Montserrat', sans-serif;
  color: #7F7F7F;
  margin: 0px;
  background-color: rgba(249, 249, 249, 1);
  background-image: none;
  font-weight: 200;
}

#bg {
  position: fixed; 
  top: -50%; 
  left: -50%; 
  width: 200%; 
  height: 200%;
}
#bg img {
  position: absolute; 
  top: 0; 
  left: 0; 
  right: 0; 
  bottom: 0; 
  margin: auto; 
  min-width: 50%;
  min-height: 50%;
}

.navbar {
  box-shadow: 0px 4px 8px rgb(215 215 215 / 35%);
}

@media (min-width: 992px) {
  .navbar-expand-lg .navbar-nav .nav-link {
      padding-right: 1.5rem;
      padding-left: 2.5rem;
  }
}

.nav-item.dropdown:hover .dropdown-menu {
  display: block;
}

.dropdown-toggle::after {
  display:none;
}

.justify-content-end .dropdown-toggle::after {
  display: inline-block;
}

.alert-header{
  font-weight: bold;
  color: #fff;
  background-color: #db3e20;
}

.alert-header-andamento{
  font-weight: bold;
  color: #000;
  background-color: #FFC107;
}

.alert-detalhes a{
  font-size: small;
  text-decoration: none;
  color: #fff;
}
.alert-detalhes-andamento a{
  font-size: small;
  text-decoration: none;
  color: #000;
}

.alert-detalhes a:hover{
  font-size: small;
  text-decoration: none;
  filter: brightness(0.9);
}
.alert-detalhes-andamento a:hover{
  font-size: small;
  text-decoration: none;
  filter: brightness(0.9);
}

.alert-footer{
  background-color: #db3e20;
}

.alert-footer-emAndamento{
  background-color: #FFC107;
}
.nome-fazenda{
  font-size: large;
}

.ende-fazenda{
  font-weight: 100;
}

.alert-tempo{
  color:#db3e20;
}

.azul-principal{
  background-color: #0076c1;
}

.emAndamento{
  text-decoration: none;
  color: #fff;
  font-weight: bold;
  
}
.emAndamento:hover{
  text-decoration: none;
  color: #fff;
  filter: brightness(0.9);
  font-weight: bold;
}

.pronto{
  text-decoration: none;
  color: #000;
  font-weight: bold
}
.pronto:hover{
  text-decoration: none;
  color: #000;
  filter: brightness(0.9);
  font-weight: bold;
}

.btn-alerta{
  background-color: #db3e20;
  border: #db3e20;
}

.btn-alerta:hover, .btn-alerta:disabled{
  background-color: #db3e20;
  border: #db3e20;
  filter: brightness(0.9);
}

/* .btn-alerta::after{
  background-color: #db3e20;
  border: #db3e20;
  filter: brightness(0.9);
} */

.mapa {
  border-radius: 30px;
  position: relative;
  width:450px
}

.marker  span {
  display:flex;
  justify-content:center;
  align-items:center;
  box-sizing:border-box;
  width: 30px;
  height: 30px;
  color:#fff;
  background: #db3e20;
  border:solid 2px;
  border-radius: 0 70% 70%;
  box-shadow:0 0 2px #000;
  cursor: pointer;
  transform-origin:0 0;
  transform: rotateZ(-135deg);
}

.marker-police  span {
  display:flex;
  justify-content:center;
  align-items:center;
  box-sizing:border-box;
  width: 30px;
  height: 30px;
  color:#fff;
  background: #43A2E6;
  border:solid 2px;
  border-radius: 0 70% 70%;
  box-shadow:0 0 2px #000;
  cursor: pointer;
  transform-origin:0 0;
}

.marker-andamento  span {
  display:flex;
  justify-content:center;
  align-items:center;
  box-sizing:border-box;
  width: 30px;
  height: 30px;
  color:#fff;
  background: #FFC107;
  border:solid 2px;
  border-radius: 0 70% 70%;
  box-shadow:0 0 2px #000;
  cursor: pointer;
  transform-origin:0 0;
  transform: rotateZ(-135deg);
}

.modal-header{
  color:#555555;
  font-weight: bold;
}

.customInput{
  border-radius: 100px !important;
  height: 50px !important;
  border: none !important;
}

.form-control{
  border-radius: 100px !important;
  height: 50px !important;
  border: none !important;
}

.customLabel{
  font-weight: bold;
  font-size: 14px;
}

.blueColor{
  background-color: #0076C1;
}

.btnCadastro{
  border-radius: 10px;
  font-size: 18px;
}

.dialogModal{
  padding: 10px;
  border-radius: 20px;
  border: none;
}

.obr{
  color: red;
  font-weight: bolder;
}

.uploadFoto{
  border-style: dashed;
  border-width: 2px;
}

.cardMap{
  border-radius: 30px;
  border: #fff;
  margin-bottom: 15px;
  
  overflow: hidden;
}

.menuRotas{
  border:#fff;
  width: 180px;
  border-radius: 20px;
  font-weight: bold;
  margin-bottom: 15px;
  cursor: pointer;
}

.cardCriar{
  background-color: #0076C1;
  color:#fff;
}

.iconMenu{
  font-size: 40px;
  color:#0076c1;
  margin-bottom: 10px;
}

.iconCriar{
  font-size: 40px;
  margin-bottom: 10px;
}

.contentMenu{
  margin-left: 15px;
  margin-top: 15px;;
}

.menu{
  margin-top: -60px;
}

.mapboxgl-ctrl-logo {
  display: none !important;
}

.contentInfo{
  margin-left: 3px;
  /* margin-top: 15px; */
  padding: 10px;
}

.menuInfos{
  border:#fff;
  border-radius: 10px;
  font-size: 15.1px;
  margin-bottom: 15px;
  cursor: pointer;
}

.contentInfoNumber{
  font-size: 25px;
  color:#0076C1;
}

.contentAtivNumber{
  font-size: 25px;
  color:#75D3C1;
}

.contentInativNumber{
  font-size: 25px;
  color:#D9D4BF;
}

.footerQr{
  background-color: #333333;
  border-radius: 30px;
}

.iconQR{
  font-size: 25px;
}

.iconQRW{
  font-size: 22px;
}

.botaoQrCode{
  width: 100%;
  border-radius: 30px;
  font-weight: bold;
}

.formError{
  margin-top:3px;
  margin-left:15px;
  font-size:12px;
  color:#db3e20;
}
.icon-police{
  font-size: 30px;
}

a{
  text-decoration: none;
}