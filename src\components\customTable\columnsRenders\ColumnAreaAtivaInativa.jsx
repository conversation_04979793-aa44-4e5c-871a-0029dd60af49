import React from 'react';
import ReactTooltip from 'react-tooltip';
import styled from 'styled-components'
import './ColumnStatusRender.css';



const ActiveOrNot = styled.div`
   width: 10px;
   height: 10px;
   border-radius: 3px;
   background-color: ${({ativo}) => ativo == "inativo" ? "#ff0000ba" : "rgba(0, 169, 86, 1)"};
`

const ColumnAreaAtivaInativa = (name, column) =>{
    

    return {
        name: name, //header do datatable   
            cell: (row,index) => { 
                console.log(row) 
                                                                
                return ( 
                    <>
                        <ActiveOrNot ativo={row[column]}  data-tip data-for={`tooltip-${index}`} className={`column-status`} />
                        <ReactTooltip place="bottom" className="text" id={`tooltip-${index}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{row[column] == "inativo" ? "inativo" : "ativo"}</span>         
                        </ReactTooltip> 
                    </>
                )
            }
    }
}

export default ColumnAreaAtivaInativa; 

