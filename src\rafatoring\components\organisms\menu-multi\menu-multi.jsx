import React, { ReactNode } from 'react';
import {ExtendButtonBase, Menu, MenuItem, MenuItemTypeMap, PopoverOrigin} from '@mui/material';

const MenuMulti = ({children, color = 'white', anchorEl, onClose = () =>{}, onClick = () => {}, transformOrigin, anchorOrigin, width = '20vw'}) =>{
  var blue = '#1d457b';  
  return(
        <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={Boolean(anchorEl)}
        onClose={onClose}
        onClick={onClick}
        PaperProps={{
          elevation: 0,
          sx: {
            width: width,
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: `${color == 'white' ? 'white' : blue}`,
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
        transformOrigin={transformOrigin}
        anchorOrigin={anchorOrigin}
        sx={{'.MuiPaper-root':{
          backgroundColor: `${color == 'white' ? 'white' : blue}`,
          color: `${color == 'white' ? '#555555' : 'white'}`,
        }}
      }
      >
        {children}

      </Menu>
    )
}

export default MenuMulti;