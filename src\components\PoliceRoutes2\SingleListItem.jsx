import React,{useEffect, useState} from 'react'
import KeyboardArrowRightIcon from '@material-ui/icons/KeyboardArrowRight';
// import LocalFireDepartmentIcon from '@material-ui/icons/LocalFireDepartment';
import WhatshotIcon from '@material-ui/icons/Whatshot';
import HotelIcon from '@material-ui/icons/Hotel';


const returnLogoWhatshotIcon = () =>(
    <div 
        style={{display:'flex',flexDirection:'column', 
        margin:0,justifyContent:"center",alignItems:'center'}}>
        <WhatshotIcon style={{fontSize:'24px',padding:0,margin:0}}/>
        <p style={{fontSize:'12px', margin:0}}>Combustivel!</p>
    </div>
)

const returnLogoHotelIcon = () =>(
    <div 
        style={{display:'flex',flexDirection:'column',
        margin:0, marginLeft:"10px", justifyContent:"center",alignItems:'center'}}>
        <HotelIcon style={{fontSize:'24px',padding:0,margin:0}}/>
        <p style={{fontSize:'12px',margin:0}}>Pernoite!</p>
    </div>
)

const returnKeyboardArrowRightIcon = () =>(
    <div 
        style={{display:'flex',flexDirection:'column',
        margin:0, marginLeft:"10px", justifyContent:"center",alignItems:'center'}}>
        <KeyboardArrowRightIcon style={{fontSize:'24px',padding:0,margin:0}}/>
    </div>
)

 function SingleListItem(props) {
    useEffect(() => {
        localStorage.setItem("routes","[]")
    }, [])
    
    const [state, setstate] = useState("")
    const [color, setcolor] = useState(0)
    const [toggle, setToggle] = useState(0)

    const appendToArr=()=>{
        setcolor(!color)
        setToggle(toggle+1)
    } 
    useEffect(() => {
        const dun = async () => {
            if(toggle){
                if(localStorage.getItem("routes") && color){
                    console.log("Area is here")
                    const data=await JSON.parse(localStorage.getItem("routes"))
                    console.log(data,"here here")
                    data.push(props.data.id_fzd)
                    // console.log("pushed data",JSON.stringify(data))
                    localStorage.setItem("routes",JSON.stringify(data))
                }
                else if(localStorage.getItem("routes") && !color){
                    console.log("Unselect here")
                    var data=await JSON.parse(localStorage.getItem("routes"))
                    console.log(data,"here here");
                    data=data.filter(e=>e!=props.data.id_fzd)
                    // console.log("pushed data",JSON.stringify(data))
                    localStorage.setItem("routes",JSON.stringify(data))
                }
                else{
                    const data=[props.data.id_fzd]
                    localStorage.setItem("routes",JSON.stringify(data))
                    console.log("New Array Created")
                }
            }
        }
        dun()
    }, [toggle])

    return (
        <div onClick={appendToArr}
         style={{fontFamily:"Montserrat",color:'#555555',display:'flex',paddingLeft:'12px', 
         marginBottom:'10px',alignItems:'center',justifyContent:'space-between', height:'auto',
         backgroundColor:color?"rgb(235,242,247)":'white'}}>

            <div style={{width:'25%'}}>
                <h6 style={{width:'100%',overflow:'hidden'}}>{props.data.nome_da_fazenda}</h6>
            </div>

            <div style={{width:'25%',display:'flex',flexDirection:'column',padding:0,margin:0}}>
                <h5 style={{padding:0,fontSize:'18px',fontWeight:100,margin:0}}>
                    {props.data.area_de_operacao}
                </h5>
                <p style={{fontWeight:100,color:'#8A8AAF',fontSize:'14px',padding:0,margin:0}}>
                    {props.data.latitude}
                    {props.data.longitude}
                </p>
            </div>
            <div style={{width:'25%'}}>
                <h5 style={{padding:0,marginLeft:"25px"}}>{props.data.id_fzd}</h5>
            </div>

            <div 
                style={{width:'25%',paddingRight:'20px',scrollbarColor:'#C1C1C1', 
                display:'flex',alignItems:'center',justifyContent:'space-between'}}>
                    <span/>              
                    {props.data.combustivel == 1 ?  returnLogoWhatshotIcon() : null}
                    {props.data.pernoite == 1 ?  returnLogoHotelIcon() : null} 

                    {returnKeyboardArrowRightIcon()}
            </div>
        </div>
    )
}

export default SingleListItem
