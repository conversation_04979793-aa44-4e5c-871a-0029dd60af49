import React,{useEffect, useState} from 'react'
import KeyboardArrowRightIcon from '@material-ui/icons/KeyboardArrowRight';
import LocalGasStationIcon from '@material-ui/icons/LocalGasStation'; //icone de combustivel

// import LocalFireDepartmentIcon from '@material-ui/icons/LocalFireDepartment';


function SingleListItem(props) {

    useEffect(() => {
        localStorage.setItem("areas","")
        localStorage.setItem("routes","[]")
    }, [])

    const [toggle, setToggle] = useState(0)
    const appendToArr=()=>{
        setcolor(!color)
        setToggle(toggle+1)
    } 

    useEffect(() => {
        const dun=async()=>{
            if(toggle){
                props.setSelect(1)
                if(toggle){
                    props.setselected(props.data.id)
                    localStorage.setItem("areas",props.data.id)
                }
                else if(!toggle){
                    props.setselected("")
                    localStorage.setItem("areas","")
                }
                else{
                    localStorage.setItem("areas","")
                }
            }}
            //     if(localStorage.getItem("areas") && color){
            //         console.log("Area is here")
            //         const data=await JSON.parse(localStorage.getItem("areas"))
            //         console.log(data,"here here")
            //         data.push({id:props.data.id})
            //         // console.log("pushed data",JSON.stringify(data))
            //         localStorage.setItem("areas",JSON.stringify(data))
            //     }
            //     else if(localStorage.getItem("areas") && !color){
            //         console.log("Unselect here")
            //         var data=await JSON.parse(localStorage.getItem("areas"))
            //         console.log(data,"here here");
            //         data=data.filter(e=>e.id!=props.data.id)
            //         // console.log("pushed data",JSON.stringify(data))
            //         localStorage.setItem("areas",JSON.stringify(data))
            //     }
            //     else{
            //         const data=[props.data]
            //         localStorage.setItem("area",JSON.stringify(data))
            //         console.log("New Array Created")
            //     }
            // }}
            
        dun()
    }, [toggle])
    
    const [state, setstate] = useState([])
    const [color, setcolor] = useState(0)


    return (
        <div onClick={appendToArr} style={{fontFamily:"Montserrat",color:'#555555',display:'flex',paddingLeft:'12px',marginBottom:'10px',alignItems:'center',height:'auto',backgroundColor:props.selected==props.data.id?"rgb(235,242,247)":'white'}}>
            <div style={{width:'25%', height:'auto'}}>
                <h5>{props.data.nome_da_area}</h5>
            </div>
            <div style={{width:'25%',display:'flex',flexDirection:'column',padding:0,margin:0}}>
                <h5 style={{padding:0,fontSize:'18px',fontWeight:100,margin:0}}>
                    {props.data.ponto_de_referencia}
                </h5>
                <p style={{fontWeight:100,color:'#8A8AAF',fontSize:'14px',padding:0,margin:0}}>
                    {props.data.raio?props.data.raio:"----"}Km
                </p>
            </div>
            <div style={{width:'25%'}}>
                <h5 style={{padding:0,marginLeft:"25px"}}>{props.data.Fazenda}</h5>            
            </div>
            <div style={{width:'25%',paddingRight:'20px',scrollbarColor:'#C1C1C1',display:'flex',justifyContent:'space-between'}}>
                <h5 style={{padding:0,marginLeft:"18px"}}> </h5>
                <KeyboardArrowRightIcon/>
            </div>
        </div>
    )
}

export default SingleListItem
