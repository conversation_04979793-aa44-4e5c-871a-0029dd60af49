import React,{useState,useEffect} from 'react'
import MenuIcon from '@material-ui/icons/Menu';
import axios from 'axios';
import { <PERSON>, <PERSON>, Col, Spin<PERSON>,Mo<PERSON>, <PERSON>ton } from 'react-bootstrap';
import { Menu } from '@material-ui/core';
import { IoClose } from "react-icons/io5"
import EditDeleteCard from '../EditDeleteCard';
import { useUserContext } from '../../context/authContext';
import { URL } from '../../services';
import KeyboardArrowRightIcon from '@material-ui/icons/KeyboardArrowRight';


import EditVehicalForm from '../../rafatoring/pages/vehicals/EditVehicalForm';

function VehicalSingle(props) {
   
     function submit(event){
    
     }
    
    
    const[open,setOpen] = useState(false);
    const [anchor, setanchor] = useState(0)
    const handleDelete=()=>{
        setanchor(null)
    }

    const lolo=()=>{
        if(props.vehical[0]===props.data.id_viat){
            return "#EBF2F7"
        }
        return "white"
    }

    const handleEdit = () => {
        setanchor(null)
        setOpen(true)
    }

    const showModal=()=>{
        return (<EditVehicalForm oopen={[open,setOpen]} data={props.data}/>)
    }


    return (
        <div onClick={()=>props.vehical[1](props.data.id_viat)} style={{display:'flex',height:"90px",marginTop:'3px',backgroundColor:lolo(),borderTop:'1px solid lightgray'}}>
            <div style={{display:'flex',width:'35%',paddingRight:'20px',alignItems:'center'}}>    
                {/* <div style={{objectFit:'contain',margin:0,padding:0,width:'100px',height:'60px'}}>
                    <img src={props.data.imagem_viat} style={{borderRadius:'50%',width:'100%'}} alt="Cars"></img>
                </div> */}
                <div style={{height:"100%",display:'flex',marginLeft:'40px',flexDirection:'column',alignItems:'flex-end',justifyContent:'center'}}>
                    <h5 style={{margin:0,padding:0}}>#{props.data.id_viat}</h5>
                    <p style={{fontSize:'14px',textAlign:'center',margin:0,padding:0}}>{props.data.modelo_viat}</p>
                </div>
            </div>
            <div style={{width:'20%',marginLeft:'72px',textAlign:'end'}}>
                <h5 style={{margin:0,lineHeight:4.5,padding:0}}>{props.data.km_inicial} km</h5>
            </div>
            <div style={{width:'20%',marginLeft:'40px',textAlign:'center'}}>
                <h5 style={{margin:0,lineHeight:4.5,marginRight:'20px',padding:0}}>--</h5>
            </div>
            <div style={{display:'flex',alignItems:'center',marginRight:'20px',width:'20px',justifyContent:'flex-end'}}>
                    <KeyboardArrowRightIcon/>
                    <Menu
                        style={{borderRadius:'10px'}}
                            anchorEl={anchor}
                            transformOrigin={{ vertical: "up", horizontal: "right" }}
                            keepMounted
                            open={anchor}
                            onClose={()=>setanchor(0)}
                        >
                            <EditDeleteCard edit={handleEdit} submit={submit} delete={handleDelete}/>
                    </Menu>
            </div>

        </div>
    )
}

export default VehicalSingle
