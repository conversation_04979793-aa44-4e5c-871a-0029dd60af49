import React, { useState, useEffect } from "react";
import { Card, Row, Col, Spinner } from 'react-bootstrap';
import axios from 'axios'
import { useAlertContext } from '../context/alertContext';
import Timer from './Timer'
import DistanciaViatura from './DistanciaViatura'

function Alert() {

    const {datas, handlePronto, handleEmAndamento} = useAlertContext();
    const dataI = [...datas];
    const dataInvert = dataI.reverse();
    console.log(dataInvert)
   

  return (
    <>
        {dataInvert.map(data => 
                <Card className="mb-2" key={data[2]}>
                    {data[3] === 'acionado' ? (
                        <Card.Header className="alert-header">
                            <Row>
                                <Col md={10} xs={6}>Botão de Pânico acionado</Col>
                                <Col md={2} xs={6} className="mr-n5 alert-detalhes"><a href='/alert'>Ver detalhes</a></Col>
                            </Row>
                        </Card.Header>)
                        :(
                        <Card.Header className="alert-header-andamento" >
                            <Row>
                                <Col md={10} xs={6}>Botão de Pânico acionado</Col>
                                <Col md={2} xs={6} className="mr-n5 alert-detalhes-andamento"><a href='/alert'>Ver detalhes</a></Col>
                            </Row>
                        </Card.Header>
                        )
                    }    
                    <Card.Body>
                        <Row>
                            <Col md={5}>
                                <div className="nome-fazenda text-dark">{data[0]}</div>
                            </Col>
                            <Col>
                                <center>
                                    {/* <Timer data={data[6]}/> */}
                                </center>
                            </Col>
                            <Col>
                                <center><div className="nome-fazenda">3</div></center>
                            </Col>
                            <Col>
                                <DistanciaViatura lat={data[4]} lng={data[5]}/>
                            </Col>
                        </Row>
                        <Row>
                            <Col md={5}>
                                <div>{data[1]}</div>
                            </Col>
                            <Col>
                                <center>Tempo acionado</center>
                            </Col>
                            <Col>
                                <center>Viaturas próximas</center>
                            </Col>
                            <Col>
                                <center>do veículo próximo</center>
                            </Col>
                        </Row>
                    </Card.Body>
                    {data[3] === 'acionado' ? (
                        <Card.Footer className='alert-footer'>
                            <center><a href="#" className='emAndamento' onClick={()=>handleEmAndamento(data[2])}>Enviar viatura mais próxima</a></center>
                        </Card.Footer>
                    ):(
                        <Card.Footer  className='alert-footer-emAndamento'>
                            <center><a href="#" className='pronto' onClick={()=>handlePronto(data[2])}>Arquivar Chamado</a></center>
                        </Card.Footer>
                    )}
                </Card>
            )
        }
    </>
  );
}

export default Alert;