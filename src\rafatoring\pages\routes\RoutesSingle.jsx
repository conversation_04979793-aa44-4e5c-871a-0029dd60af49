import React from 'react'
import ReactTooltip from 'react-tooltip';
import { useHistory } from 'react-router-dom';
import { <PERSON><PERSON>, TableCell, TableRow, MenuItem, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../components/organisms'
import { MdContentCopy } from 'react-icons/md';
import { BiDetail } from 'react-icons/bi';
import { URL } from '../../../services';
import axios from 'axios';

function RoutesSingle({getRoutes, data, page, rowsPerPage}) {

    const history = useHistory();

    const person = window.localStorage.getItem('@Ident');

    const handleDetails = (routeId) => {
        history.push('/detalherota/'+ routeId)
    }

    const getStatus=(value) => {
        switch (value.toUpperCase()) {
            case "AGUARDANDO":
                return "orange"         
            case "ENCERRADA":
                return "green"
            case "INICIADA":
                return "blue"
            default:
                return "blue"
        }
    }

    const handleDuplicateRoute = (areaId, listFarms) => {
        const now = new Date();
        axios.post(URL + 'salvar_rota', {
            "data_criacao": now.getDate() + '/' + now.getMonth() + '/' + now.getFullYear(),
            "hora_criacao": now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds(),
            "id_area": areaId,
            "id_viat": "",
            "unidade": "",
            "data_inicio": "",
            "hora_inicio": "",
            "lista_fazendas": listFarms,
            "kilometragem_estimada": "",
            "componentes_policiais": "",
            "status": "Aguardando"
        })
        .then(res => getRoutes())
        .catch(err => console.log(err))
        console.log(areaId);
        console.log(listFarms)
    }

    const handleStartRoute = (idArea, idRoute) => {
        history.push(`/InitialRoute/${idArea}/${idRoute}`);
    }

    const getDate = (date) => {
        if(date){
            console.log('date', date)
            let dateSplit = date.split('-');
            return `${dateSplit[2]}-${dateSplit[1]}-${dateSplit[0]}`
        }
        else{
            return 'Indefinido'
        }
    }

    const getFarms = (farms) => {
        if(farms){
            let farmsSplit = farms.split(',');
            if(farmsSplit.length > 1){
                return {farms: farmsSplit.length, text: 'fazendas'}
            }
            else{
                return {farms: farmsSplit.length, text: 'fazenda'}
            }
        }
        else{
            return {farms: 0, text: 'fazendas'}
        }
    }

    return (
        <>
            {data?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index) =>(
                <TableRow key={index}>
                    <TableCell style={{ minWidth:'150px'}}>
                        <h6 style={{fontWeight:'bold'}}>{getDate(i.data_inicio)}</h6>
                        <h6>{i.hora_inicio}</h6>
                    </TableCell>
                    <TableCell style={{ minWidth:'150px'}} align='center'>
                        <h6 style={{fontWeight:'bold'}}>{getFarms(i.lista_fazendas).farms}</h6>
                        <h6>{getFarms(i.lista_fazendas).text}</h6>
                    </TableCell>
                    <TableCell style={{ minWidth:'150px'}} align='center'>
                        <h6 style={{fontWeight:'bold'}}>{i.nome_da_area}</h6>
                    </TableCell>
                    <TableCell style={{ minWidth:'150px'}} align='center'>
                        <h6 style={{fontWeight:'bold'}}>{
                            person == 'ADMINAIBA' 
                                ? 
                                i.id_viat
                                    ? 
                                    `#${i.id_viat}`
                                    :
                                    'Sem Viatura'
                                :
                                i.id_viat
                                    ?
                                    `#${i.id_viat}`
                                    :
                                    <Button variant="outlined" onClick={()=> handleStartRoute(i.id_area, i.id)}>Iniciar Serviço</Button>
                        }</h6>
                        <h6>{i.componentes_policiais}</h6>  
                    </TableCell>
                    <TableCell align='center'>
                        <ReactTooltip place="bottom" className="text" id={`tooltip-${i.id}`} aria-haspopup='true' type="dark" effect='solid'>            
                            <span>{i.status}</span>          
                        </ReactTooltip>
                        <div 
                            data-tip 
                            data-for={`tooltip-${i.id}`}
                            style={{
                                width:'10px',
                                height:'10px', 
                                backgroundColor:getStatus(i.status), 
                                borderRadius:'3px',
                                marginLeft: '15px'
                            }}>
                        </div>
                    </TableCell>
                    <TableCell>
                        <TableItemMenuMulti>
                            {person == 'ADMINPM'
                                &&
                                <>
                                    <MenuItem onClick={() => handleDuplicateRoute(i.id_area, i.lista_fazendas)}>
                                        <ListItemIcon><MdContentCopy size={20}/></ListItemIcon>
                                        Duplicar Rota
                                    </MenuItem>
                                </>
                            }
                            <MenuItem onClick={() => handleDetails(i.id)}>
                                <ListItemIcon><BiDetail size={20}/></ListItemIcon>
                                Detalhes da rota
                            </MenuItem>
                            {/* <MenuItem onClick={() => handleDetails(i.id_fzd)}>
                                <ListItemIcon><MdOutlineDelete size={20}/></ListItemIcon>
                                Excluir
                            </MenuItem> */}
                        </TableItemMenuMulti>
                    </TableCell>
                </TableRow>
            ))}
        </>
    )
}

export default RoutesSingle
