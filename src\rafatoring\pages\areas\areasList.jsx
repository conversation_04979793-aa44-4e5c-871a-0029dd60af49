import React, { useEffect, useState } from 'react';
import { TableComponent } from '../../components/organisms';
import { TableHead, TableRow, TableCell, TableBody, FormControl, Input, InputAdornment } from '@mui/material';
import axios from 'axios';
import { URL } from '../../../services';
import AreasSingle from './areasSingle';
import SearchBar from 'material-ui-search-bar';
import { AiOutlineSearch } from 'react-icons/ai';

const AreasList = () => {
const [areas, setAreas] = useState([]);
const [page, setPage] = useState(0);
const [rowsPerPage, setRowsPerPage] = useState(5);
const [search, setSearch] = useState('');

const getAreas = () =>{
  axios.get(URL+'todas_areas')
  .then(res=>{
      setAreas(res.data)
    })
    .catch(err=>console.log(err))
  }


  useEffect(() => {
    getAreas();
  },[])

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

    return (  
        <>
            {areas
                ?
                <TableComponent 
                    count={areas.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title={`Existem ${areas.length} áreas de atuação`}
                    subtitle={
                      <>
                        <FormControl sx={{ mt: 0.4 }} variant="outlined">
                              <Input
                                  variant="standard"
                                  placeholder='Pesquisar'
                                  size='small' 
                                  value={search}
                                  onChange={(value) => setSearch(value.target.value)}
                                  endAdornment={
                                      <InputAdornment position='end'>
                                        <AiOutlineSearch size={20}/>
                                      </InputAdornment>
                                  }
                              />
                        </FormControl>    
                      </>
                    }
                >
                    <TableHead>
                        <TableRow>
                            <TableCell>Nome da Área</TableCell>
                            <TableCell align='center'>Localização</TableCell>
                            <TableCell align='center'>Fazendas</TableCell>
                            <TableCell align='center'>Viatura</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        <AreasSingle page={page} rowsPerPage={rowsPerPage} getAreas={getAreas} areas={areas.filter(e => e.nome_da_area.toLowerCase().includes(search.toLowerCase()) || e.ponto_de_referencia.toLowerCase().includes(search.toLowerCase()))}/>
                    </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>             
    ); 
}

export default AreasList;



