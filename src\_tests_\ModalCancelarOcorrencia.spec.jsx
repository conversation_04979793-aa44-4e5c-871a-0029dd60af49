import React from 'react';
import ModalCancelarOcorrencia from '../components/ModalCancelarOcorrencia'
import {render} from "@testing-library/react";

describe("Testing Ocorrencias", () => {

  it("Modal cancelar ocorrencia", () => {
      const screen = render(<ModalCancelarOcorrencia
        row={{
          "id_ocorrencia": 12,
          "justificativa": "não há necessidade de manter a ocorrência devido sua natureza"
        }}/>);

        const titulo = screen.findByText('Cancelar Ocorrência');

        expect(titulo).toBeTruthy();

      expect(screen).toBeTruthy();
  })
})