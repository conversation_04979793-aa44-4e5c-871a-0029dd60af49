import React,{useState, useEffect} from 'react'
import MenuIcon from '@material-ui/icons/Menu';
import { Menu } from '@material-ui/core';
import PanicOptions from '../../../../components/PanicOptions';
import ReactTooltip from 'react-tooltip';
import { TableRow, TableCell, MenuItem, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../../components/organisms';
import { useHistory } from 'react-router-dom';
import { BiDetail } from 'react-icons/bi'

function PanicSingle({panicos, fazendas, areas, page, rowsPerPage}) {

    const history = useHistory();

    const getDate = (date) => {
        let dateSplit = date.split("T")
        return dateSplit;
    }

    const getFazenda = (nomeFazenda) => {
        let fazendaFilter = fazendas.filter((fazenda) => {
            return fazenda.id_fzd == nomeFazenda
        })
        return fazendaFilter[0];
    }

    const getArea = (nomeArea) => {
        let areaFilter = areas.filter((area) => {
            return area.id = nomeArea
        })
        return areaFilter[0]
    }

    const handleStatus = (status) => {
      if(status == 0){
        return {cor: "orange", texto: "Aguardando atendimento"};
      }
      if(status == 1){
        return {cor: "green", texto: "Em andamento"};
      }
      if(status == 2){
        return {cor: "blue", texto: "Finalizado"};
      }
      if(status == 3){
        return {cor: "red", texto: "Cancelado"}
      }
    }

    const handleClickDetails = (id) => {
        history.push('/alertdetail/' + id)
    }

    return (
      <>
        {panicos?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index)=>(
            <TableRow key={index}>
                <TableCell>
                    <h6 style={{fontWeight: 'bold'}}>{new Date(getDate(i.HORA)[0]).toLocaleDateString()}</h6>
                    <h6>{getDate(i.HORA)[1] ? getDate(i.HORA)[1].split(".")[0] : getDate(i.HORA)[1]}</h6>
                </TableCell>
                <TableCell>
                    <h6 style={{fontWeight: 'bold'}}>{getFazenda(i.NOME_FAZENDA) ? getFazenda(i.NOME_FAZENDA).nome_da_fazenda : "Nome"}</h6>
                    <h6>{getFazenda(i.NOME_FAZENDA) ? getFazenda(i.NOME_FAZENDA)?.nome_do_proprietario : "Nome"}</h6>
                </TableCell>
                <TableCell>
                    <h6 style={{fontWeight: 'bold'}}>{getArea(i.NOME_AREAFZD) !== undefined ? getArea(i.NOME_AREAFZD).nome_da_area: "Nome da área"}</h6>
                    <h6>{i.LATITUDE + "; " + i.LONGITUDE}</h6>
                </TableCell>
                <TableCell>
                    <h6 style={{fontWeight: 'bold'}}>Viatura</h6>
                    <h6>{i.VIATURA || "Viatura não definida"}</h6>
                </TableCell>
                <TableCell>
                    <ReactTooltip place="bottom" className="text" id={`tooltip-${i.ID}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{handleStatus(i.STATUS).texto}</span>          
                    </ReactTooltip>
                    <div 
                        data-tip 
                        data-for={`tooltip-${i.ID}`}
                        style={{
                            width:'10px',
                            height:'10px', 
                            backgroundColor: handleStatus(i.STATUS).cor,
                            borderRadius:'3px'
                        }}>
                    </div>
                </TableCell>
                <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem onClick={() => handleClickDetails(i.ID)}>
                            <ListItemIcon><BiDetail size={20}/></ListItemIcon>
                            Detalhes do Chamado
                        </MenuItem>
                    </TableItemMenuMulti>
                </TableCell>
            </TableRow>
        ))}
      </>
    )
}

export default PanicSingle
