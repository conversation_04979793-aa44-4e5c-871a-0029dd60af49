import React from 'react';
import { Redirect, Route } from 'react-router-dom';
import { useUserContext } from './context/authContext';

function PrivateRoute({ component: Component, ...rest }) {
  const { isAuthenticated } = useUserContext();
  const id = window.localStorage.getItem('@Ident')
  return <Route {...rest} render={props => (
    id ? (
      <Component {...props} />
    ) : (
      <Redirect to={{ pathname: '/', state: { from: props.location } }} />
    )
    )}
    />;
}

export default PrivateRoute;