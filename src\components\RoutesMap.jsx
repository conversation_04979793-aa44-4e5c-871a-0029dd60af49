import React, {useContext, useEffect} from 'react';
import Footer from '../components/Footer'
import Map from '../components/Mapa';
import Header from '../components/Header';

import { Container, Row, Col, Card, Spinner } from 'react-bootstrap'
// import Menu from '../components/menuRapido';

import { PREV, URL } from '../services';
import CustomButton from '../components/CustomButton';
import CustomButtonCriar from '../components/CustomButtonCriar'
import { useState } from 'react';
import { URL_API } from '../services';
import axios from 'axios'


function RoutesMap(props) {
    const [areasAtuacao, setAreasAtuacao] = useState([]);

    const ident = window.localStorage.getItem('@Ident');

  useEffect(()=>{
    async function fetchData(){
        const areasResponse = await axios.get(URL_API+"todas_areas")
        setAreasAtuacao(areasResponse.data)
    }
    fetchData();
  },[]
);
    return (
<Card className='cardMap shadow' style={{width:'420px'}} id='cardMap'>
{/* <Card.Header className='cardMapHeader'>
  Resumo por Área
</Card.Header> */}
<h5 className='mt-4 px-3 mb-3 text-dark'>Resumo por Área</h5>
    <Map height={650} largura={"200"} areasAtuacao={areasAtuacao} setAreaSelected={(e) => props.setAreaSelected(e)}/>
    <div className='mx-auto menu'>
    <Row >
        {ident == "ADMINPM" ? 
            <Col md='6' sm='6' lg='6'>
                <CustomButtonCriar tipo="rotas"/>
            </Col> : <></>}
        
            <Col md='6' sm='6' lg='6'>
            <CustomButton tipo='fazendas'/>
            </Col>
        </Row>
        <Row>
            <Col md='6' sm='6' lg='6'>
            <CustomButton tipo='rotas'/>
            </Col>
            <Col md='6' sm='6' lg='6'>
            <CustomButton tipo='policiais'/>
            </Col>
        </Row>
    </div>
    </Card>)}
 
      

export default RoutesMap

