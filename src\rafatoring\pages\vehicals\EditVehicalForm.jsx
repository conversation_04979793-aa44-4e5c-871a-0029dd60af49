import React, {useContext, useEffect, useState} from 'react';
import Header from '../../../components/Header'
import Footer from '../../../components/Footer'

import { useUserContext } from '../../../context/authContext';

import axios from 'axios';

import { DropzoneDialog } from 'material-ui-dropzone';
import { AiOutlineCloudUpload } from "react-icons/ai"
import { Typeahead } from 'react-bootstrap-typeahead';

import { 
    Container,
    Row, 
    Col, 
    Card, 
    Form, 
    Button, 
    InputGroup,
    FormControl,
    Spinner
} from 'react-bootstrap'
import { URL } from '../../../services';
import Swal from 'sweetalert2';

function EditVehicalForm(props) {

    const [open, setOpen] = useState(false);
    const [load, setLoad] = useState(false)
    const [modelo, setModelo] = useState(props.data.modelo_viat);
    const [placa, setPlaca] = useState(props.data.placa);
    const [kmI, setKmI] = useState(props.data.km_inicial);
    const [setor, setSetor] = useState(props.data.setor);
    const [tipoC, setTipoC] = useState(props.data.tipo_combustivel);
    const [unidade, setUnidade] = useState(props.data.unidade);
    const [vtr,setVtr] = useState(props.data.situacao_vtr);
    const [area, setArea] = useState(props.data.area_atuacao);
    const [foto, setFoto] = useState(props.data.imagem_viat);
    const [status, setStatus] = useState(props.data.ativa_inativa); 
    const [areasOp, setAreasOp] = useState([]);

    console.log(area, unidade)

    const id=props.data.id_viat

    const {setTextHeader,toggle,settoggle} = useUserContext();

    setTextHeader('Cadastrar Viaturas');
    useEffect(()=>{
        axios.get(URL+'todas_areas')
        .then(res=>{
            const itensAreas = res.data
            const optionsAreasRequest = itensAreas.map((item)=>({
                id:item.id,
                name: item.nome_da_area
            }))
            setAreasOp(optionsAreasRequest)
        })
        .catch(err=>console.log(err))
    },[])

    function submit(event){
        event.preventDefault();
        setLoad(true)
        axios.put(URL+'atualizar_viatura',{
            id_viat:id,
            modelo_viat:modelo,
            placa: placa,
            km_inicial: kmI,
            tipo_combustivel: tipoC,
            unidade: unidade,
            situacao_vtr: vtr,
            setor: setor,
            area_atuacao: area,
            imagem_viat: foto.name || foto ,
            ativa_inativa: status

        })
        .then(res=>{
            // const data = new FormData()
            //     data.append('file', foto);
            //     data.append('tela', 'viatura');
            //     data.append('id', res.data.last_id)
            
            // axios.post(URL+'file/',data,{
            //     headers: {
            //     "Content-Type": "multipart/form-data",
            //     },
            // })
            // .then(res=>{
                Swal.fire(modelo,' cadastrado com sucesso!','success')
                setTipoC('')
                setUnidade('')
                setVtr('')
                setPlaca('')
                setKmI('')
                setLoad(false)
                props.oopen[1](false)
                settoggle(!toggle)
            // })
            // .catch(err => {
                // console.log(err)
                // setLoad(false)
                // Swal.fire('Erro','Tente novamente!','error')
            // })
        })
        .catch(err=>{
            console.log(err);
            console.log('erro')
            setLoad(false)
            console.log(err)
            Swal.fire('Erro','Tente novamente!','error')
        })
    }
  
  return (
    <>
            <Form onSubmit={submit}>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Modelo da Viatura <font className='obr'>*</font></Form.Label>
                            <Form.Control value={modelo} className='customInput shadow form-control' type="text" placeholder="Digite aqui" onChange={e => setModelo(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Placa <font className='obr'>*</font></Form.Label>
                            <Form.Control value={placa} className='customInput shadow form-control' type="name" placeholder="Digite aqui" onChange={e => setPlaca(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Kilometro Inicial <font className='obr'>*</font></Form.Label>
                            <Form.Control value={kmI} className='customInput shadow form-control' type="number" placeholder="00" onChange={e => setKmI(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Tipo de Combustivel <font className='obr'>*</font></Form.Label>
                            <Form.Control value={tipoC} className='customInput shadow form-control' type="text" placeholder="Digite aqui" onChange={e => setTipoC(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Setor/ Área de Operação <font className='obr'>*</font></Form.Label>
                            <Form.Control value={setor} className='customInput shadow form-control' type="text" placeholder="Digite aqui" onChange={e => setSetor(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicEmail">
                            <Form.Label className='customLabel'>Unidade <font className='obr'>*</font></Form.Label>
                            <select className='customInput shadow form-control' value={unidade} onChange={e=>setUnidade(e.target.value)}>
                                <option selected value='CPRO'>CPRO</option>
                                <option value='CIPT-O'>CIPT-O</option>    
                                <option value='85a. CIPM'>85a. CIPM</option>    
                                <option value='86a. CIPM'>86a. CIPM</option>    
                                <option value='CIPRv'>CIPRv</option>    
                                <option value='CIPE CERRADO'>CIPE CERRADO</option>     
                            </select>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicEmail">
                            <Form.Label className='customLabel'>Situação VTR <font className='obr'>*</font></Form.Label>
                            <select className='customInput shadow form-control' value={vtr} onChange={e=>setVtr(e.target.value)}>
                                <option selected value='Locada pela AIBA'>Locada pela AIBA</option>
                                <option value='Própria da Unidade'>Própria da Unidade</option>    
                                <option value='Locada pelo Estado'>Locada pelo Estado</option>
                            </select>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Label className='customLabel'>Imagem da Viatura <font className='obr'>*</font></Form.Label>
                        <br/>
                        <Button variant='outline-secondary' className='customInput shadow uploadFoto mb-3' style={{width:455}} onClick={() => setOpen(true)}>{foto ? foto.name || foto : <div>Faça o Upload <AiOutlineCloudUpload style={{fontSize:25}}/></div>}</Button>
                        <DropzoneDialog
                            acceptedFiles={['image/*']}
                            cancelButtonText={"cancel"}
                            submitButtonText={"submit"}
                            open={open}
                            onClose={() => setOpen(false)}
                            onSave={(files) => {
                                setFoto(files[0])
                                setOpen(false);
                            }}
                            showPreviews={true}
                            showFileNamesInPreview={true}
                            dropzoneText={"Clique, ou arraste e solte a imagem aqui"}
                        />
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Área de Atuação <font className='obr'>*</font></Form.Label>
                            {/* <Form.Control className='customInput shadow form-control' type="text" placeholder="Digite aqui" onChange={e=>setArea(e.target.value)} required/> */}
                            {areasOp.length > 0 &&
                                <Typeahead
                                id="basic-typeahead-single"
                                defaultSelected={areasOp.filter(i => i.id == area)}
                                labelKey={option => option.name}
                                onChange={selected=>{
                                    setArea(selected[0]?.id)
                                    console.log(selected)
                                }}
                                options={areasOp}
                                placeholder="Digite aqui"
                                className='customInput shadow'
                                />
                            }
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicEmail">
                            <Form.Label className='customLabel'>Status <font className='obr'>*</font></Form.Label>
                            <select className='customInput shadow form-control' value={status} onChange={e=>setStatus(e.target.value)}>
                                <option selected value="ATIVO">ATIVO</option>
                                <option value="INATIVO">INATIVO</option>       
                            </select>
                        </Form.Group>
                    </Col>
                </Row>
                <div class="col text-center mt-3">
                    <Button className='blueColor btnCadastro' type="submit" size='lg'>
                        {load 
                            ? 
                            <Spinner animation="border" />
                            :
                            'Editar Viatura'
                        }
                    </Button>
                </div>
            </Form>
    </>
  );
}

export default EditVehicalForm;