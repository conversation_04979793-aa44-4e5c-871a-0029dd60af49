import React, {useState} from 'react';
import { useUserContext } from '../context/authContext';
import { Navbar, Nav, NavDropdown, Container, Form, FormControl, Dropdown, Collapse, Button } from 'react-bootstrap'
import styled from 'styled-components'
import { useAlertContext } from '../context/alertContext';

import { IoIosLogOut } from "react-icons/io"
import { RiAccountCircleLine } from "react-icons/ri"
import { PREV } from '../services';
import { MdEdit } from 'react-icons/md'
import {RiDeleteBin6Line} from 'react-icons/ri'
import BotaoPanico from './BotaoPanico';
import CustomLinkCriar from './CustomLinkCriar';
import Aiba from '../assets/img/AIBA (1) (3).jpg'
import { useHistory } from 'react-router';


const DividerSmooth = styled.div`
  width: 2px;
  height: 34px;
  background: rgba(153, 153, 153, 0.137);
  display: none;
  @media (min-width: 992px) {
    display: block;
  }
`;


function Header() {

  const history = useHistory();

  const { userRole, handleUserLogout} = useUserContext();
  const {datas} = useAlertContext();
  const ident = window.localStorage.getItem('@Ident');
  const nome = window.localStorage.getItem('@Nome')
  const PrimeiroNome = nome? nome.split(' ') : [''];
  const handleLogout = () =>{
    handleUserLogout();
  }
  
  return (
    <>
      <Navbar  bg="white" expand="lg">
        <Container>
          <Navbar.Brand href="/home">
            <img src={Aiba} alt="" width="130" className="d-inline-block align-text-top"/>
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse>
            <Nav className="mr-auto">

              <DividerSmooth />
              {
                ident === 'COORDPM' || ident === 'ADMINPM' ? <Nav.Link onClick={()=> history.push('/rotas')}>Rotas</Nav.Link> : ""
              }
              
              {
                //PREV === 'aiba' ?
                ident === 'ADMINAIBA' ?  
                  <NavDropdown title="Fazendas" id="fazendas-nav-dropdown" renderMenuOnMount={true}>
                    <NavDropdown.Item onClick={()=> history.push('/fazendas')}>Fazendas Cadastradas</NavDropdown.Item>
                    <CustomLinkCriar tipo='fazendas'/>
                  </NavDropdown>
                : 
                <Nav.Link onClick={()=> history.push('/fazendas')}>Fazendas</Nav.Link>
              }


              {
                //PREV === 'aiba' &&
                ident === 'ADMINAIBA' &&
                <NavDropdown title="Proprietários" id="proprietarios-nav-dropdown" renderMenuOnMount={true}>
                  <NavDropdown.Item onClick={()=> history.push("/property")}>Proprietários Cadastrados</NavDropdown.Item>
                  <CustomLinkCriar tipo='prop'/>
                </NavDropdown>
              }
                <NavDropdown title="Chamados" id="chamados-nav-dropdown" renderMenuOnMount={true}>
                  <NavDropdown.Item
                    onClick={()=> history.push('/alert')}>Chamados<span 
                    class="badge rounded-pill badge-notification bg-danger">
                    {datas.length}</span>
                  </NavDropdown.Item>
                  {
                      ident === "COORDPM" || ident === "ADMINPM" ?
                    <>
                      <NavDropdown.Item
                        onClick={()=> history.push('/ocorrencias')}>Listagem de Ocorrências
                      </NavDropdown.Item>
                      <NavDropdown.Item
                        onClick={()=> history.push('/information')}>Listagem de Informações
                      </NavDropdown.Item>
                      <CustomLinkCriar tipo=''/>
                    </>
                  : <></>
                }
                
                </NavDropdown>

              {/* <Nav.Link onClick={()=> history.push('/alert')}>Chamados<span class="badge rounded-pill badge-notification bg-danger">{datas.length}</span></Nav.Link> */}

              {ident !== "ADMINAIBA" ? <></> : <Nav.Link style={{minWidth: '200px'}} onClick={()=> history.push('/areas')}>Áreas de Atuação</Nav.Link>}
              
              {
                //PREV === "coord-pm" ? 
                ident === "COORDPM" || ident === "ADMINPM" ?
                  <>
                    <NavDropdown title="Cadastros" id="cadastros-nav-dropdown" renderMenuOnMount={true}>
                      <NavDropdown.Item onClick={()=> history.push('/viaturas')}>Viaturas Cadastradas</NavDropdown.Item>
                      <CustomLinkCriar tipo='viatura'/>
                      <hr/>
                      <NavDropdown.Item  onClick={()=> history.push('/policiais')}>Policiais Cadastrados</NavDropdown.Item>
                      <CustomLinkCriar tipo='policiais'/>
                    </NavDropdown>
                  </>
                : <></>
              }

              {
                //Relatórios 
                ident === "ADMINAIBA" || ident === "ADMINPM" ?
                  <>
                    <Nav.Link title="Relatórios" id="relatorios-nav-dropdown"  onClick={()=> history.push('/reports')} renderMenuOnMount={true}>
                      Relatórios
                    </Nav.Link>
                  </>
                : <></>
              }

            </Nav>
          </Navbar.Collapse>
          
          <Navbar.Collapse className="justify-content-end">
              <Nav>
                {/* <Nav.Link className='mt-1'><i className="far fa-bell"></i><span class="badge rounded-pill badge-notification azul-principal">{datas.length}</span></Nav.Link> */}
                <Nav.Link>
                  {/* <BotaoPanico/> */}
                </Nav.Link >
                <NavDropdown
                  id="nav-dropdown-dark-example"
                  title={'Olá, '+ PrimeiroNome[0]}
                  menuVariant="dark"
                  className='mt-1'
                >
                  {/* <Dropdown.Item as="button"><RiAccountCircleLine style={{fontSize:15, marginBottom:3}}/> Minha Conta</Dropdown.Item> */}
                  <Dropdown.Item as="button" onClick={handleLogout}><IoIosLogOut style={{fontSize:15, marginBottom:3}}/> Sair</Dropdown.Item>
                </NavDropdown>
                {/* <Nav.Link className='mt-2'>Olá, <b>{PrimeiroNome[0]}</b></Nav.Link> */}
                {/* <Nav.Link className='mt-2' onClick={handleLogout}>Sair</Nav.Link> */}
              </Nav>
          </Navbar.Collapse>

          </Container>
        </Navbar>
      {/* Vai variar dependendo da ROLE do usuário */}
      <Navbar  bg="white" expand="lg" id="subheader" style={{marginTop: 1, height: '79px'}}>

        {window.location.pathname === '/home' 
          && 
          (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Seja bem vindo(a) {PrimeiroNome[0]}</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">
                <Form className="">
                  <Form.Control
                    type="search"
                    placeholder="Pesquisar..."
                    className="shadow-sm"
                    aria-label="Pesquisar"
                  />
                </Form>
              </Navbar.Collapse>
            </Container>)
          }
          {/* {
            window.location.pathname === '/rotas'
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Rotas existentes</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                <Dropdown>
                  <Dropdown.Toggle variant="secondary" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text>Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> 
              </Navbar.Collapse>
            </Container>)
          } */}
            {
            window.location.pathname === '/PoliceRoutes'
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Crie uma nova rota</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                  <div style={{display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',width:'300px',height:'80px'}}>
                    <div style={{display:'flex'}}>
                        <div style={{color:'white',backgroundColor:'#0076C1',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          01
                        </div>
                        <hr style={{width:"130px"}}/>
                        <div style={{color:'#AAB4C3',backgroundColor:'#D3DFEA',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          02
                        </div>
                    </div>
                    <div style={{display:'flex',padding:'2px',width:'250px',margin:0,justifyContent:'space-between',fontSize:'12px'}}>
                      <p style={{color:'#0076C1',marginTop:'4px',marginLeft:'10px',margin:0,padding:0}}>Área da rota</p>
                      <p style={{color:'#AAB4C3',marginTop:'4px',margin:0,padding:0}}>Fazendas da rota</p>
                    </div> 
                  </div>

             </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/InitialRoute'
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Iniciar rota</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                  <div style={{display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',width:'300px',height:'80px'}}>
                    <div style={{display:'flex'}}>
                        <div style={{color:'white',backgroundColor:'#0076C1',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          01
                        </div>
                        <hr style={{width:"130px"}}/>
                        <div style={{color:'#AAB4C3',backgroundColor:'#D3DFEA',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          02
                        </div>
                    </div>
                    <div style={{display:'flex',padding:'2px',width:'250px',margin:0,justifyContent:'space-between',fontSize:'12px'}}>
                      <p style={{color:'#0076C1',marginTop:'4px',marginLeft:'10px',margin:0,padding:0}}>Informações da Rota</p>
                      <p style={{color:'#AAB4C3',marginTop:'4px',margin:0,padding:0}}>Escolher Viatura</p>
                    </div> 
                  </div>

             </Navbar.Collapse>
            </Container>)
          } {
            window.location.pathname === '/InitialRoute2'
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Iniciar rota</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                  <div style={{display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',width:'300px',height:'80px'}}>
                    <div style={{display:'flex'}}>
                        <div style={{color:'white',backgroundColor:'#88C4A7',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          01
                        </div>
                        <hr style={{width:"130px"}}/>
                        <div style={{color:'white',backgroundColor:'#0076C1',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          02
                        </div>
                    </div>
                    <div style={{display:'flex',padding:'2px',width:'250px',margin:0,justifyContent:'space-between',fontSize:'12px'}}>
                      <p style={{color:'#0076C1',marginTop:'4px',marginLeft:'10px',margin:0,padding:0}}>Informações da Rota</p>
                      <p style={{color:'#AAB4C3',marginTop:'4px',margin:0,padding:0}}>Escolher Viatura</p>
                    </div> 
                  </div>

             </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/PoliceRoutes2'
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Crie uma nova rota</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                  <div style={{display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',width:'300px',height:'80px'}}>
                    <div style={{display:'flex'}}>
                        <div style={{color:'white',backgroundColor:'#88C4A7',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          01
                        </div>
                        <hr style={{width:"130px"}}/>
                        <div style={{color:'white',backgroundColor:'#0076C1',paddingTop:'6px',height:"30px",fontSize:'12px',textAlign:'center',width:'30px',borderRadius:'50%'}}>
                          02
                        </div>
                    </div>
                    <div style={{display:'flex',padding:'2px',width:'250px',margin:0,justifyContent:'space-between',fontSize:'12px'}}>
                      <p style={{color:'#0076C1',marginTop:'4px',marginLeft:'10px',margin:0,padding:0}}>Área da rota</p>
                      <p style={{color:'#AAB4C3',marginTop:'4px',margin:0,padding:0}}>Fazendas da rota</p>
                    </div> 
                  </div>

             </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/fazendas' 
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Fazendas Cadastradas</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                {/* <Dropdown>
                  <Dropdown.Toggle variant="secondary" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text >Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> */}
              </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/rotas' 
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Rotas existentes</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                {/* <Dropdown>
                  <Dropdown.Toggle variant="secondary" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text >Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> */}
              </Navbar.Collapse>
            </Container>)
          }
           {
            window.location.pathname === '/viaturas' 
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Viaturas cadastradas</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                {/* <Dropdown>
                  <Dropdown.Toggle variant="secondary" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text >Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> */}
              </Navbar.Collapse>
            </Container>)
          }
           {
            window.location.pathname === '/property' 
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Proprietários cadastrados</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                {/* <Dropdown>
                  <Dropdown.Toggle variant="disabled" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text>Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> */}
              </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/alert' 
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Relatório de botão de pânico acionado</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                {/* <Dropdown>
                  <Dropdown.Toggle variant="disabled" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text>Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> */}
              </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/registeredUsers' 
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Usuários cadastrados</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                {/*<Dropdown>
                  <Dropdown.Toggle variant="disabled" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text>Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> */}
              </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/detalhefazenda'
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Detalhes da Fazenda</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">
              </Navbar.Collapse>
            </Container>)
          }
          {
            window.location.pathname === '/areas'
            &&
            (<Container>
              <Navbar.Text style={{fontSize: '18px'}}>Áreas de Atuação cadastradas</Navbar.Text>
              <Navbar.Collapse className="justify-content-end">

                {/* <Dropdown>
                  <Dropdown.Toggle variant="secondary" id="dropdown-basic">
                    7 dias
                  </Dropdown.Toggle>

                  <Dropdown.Menu style={{padding: 30, borderRadius: 10}}>
                    <Navbar.Text>Filtre o período</Navbar.Text>
                    <Dropdown.Divider />
                    <Dropdown.Item as="button">Últimos 7 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 15 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 30 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Últimos 60 dias</Dropdown.Item>
                    <Dropdown.Item as="button">Personalizar</Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown> */}
              </Navbar.Collapse>
            </Container>)
          }
          {
          window.location.pathname === '/reports' 
          &&
          (<Container>
            <Navbar.Text style={{fontSize: '18px'}}>Relatórios</Navbar.Text>
            <Navbar.Collapse className="justify-content-end">
            </Navbar.Collapse>
          </Container>)
          }
        </Navbar>
    </>
  );
}

export default Header;