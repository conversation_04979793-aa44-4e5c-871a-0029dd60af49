import React from 'react'
import { Card, Row, Col, Spinner } from 'react-bootstrap';


function PropertyStats({color,title,text}) {
    return (
        <>
            <Card style={{width:"300px",height:'180px',borderRadius:"10px"}} className='shadow m-2 ' id='cardMap'>
                <Card.Body>
                <Card.Title style={{fontWeight:'100',color:'#8F7F93',fontSize:'14px'}}>
                        {title}
                    </Card.Title>
                    <Card.Text className={color} style={{fontSize:'26px', font: 'ArialMT', fontWeight:'500', fontStyle: 'normal' }}>
                        {text}
                    </Card.Text>
                </Card.Body>
            </Card>
        </>

    )
}

export default PropertyStats
