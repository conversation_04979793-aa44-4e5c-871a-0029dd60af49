import React, {useContext, useEffect} from 'react';
import axios from 'axios';

import {SocketContext} from '../context/socketContext';
import Header from '../components/Header'
import Footer from '../components/Footer'
import Alert from '../components/Alert'
import Geolocation from '../components/Geolocation'
import {usePositionContext} from '../context/locationContext';
import Map from '../components/Mapa';

import { Container,Row, Col, Card } from 'react-bootstrap'
import { PREV, URL, URL_API } from '../services';
import CustomButton from '../components/CustomButton';
import { useState } from 'react';
import PropertyStats from '../components/PropertyStats';
import FilterProperty from '../components/FilterProperty';
import MapAndList from '../components/MapAndList';
import CustomButtonCriar from '../components/CustomButtonCriar';


function PropertyCadastraods() {
    const ident = window.localStorage.getItem('@Ident')
    const [numProp, setNumProp] = useState(0);
    const [numFzd, setNumFzd] = useState(0);
    useEffect(()=>{
        axios.get(URL+'selecionar_todos_proprietarios')
        .then(res=>{
            setNumProp(res.data.length);
        })
        axios.get(URL+'todas_fazendas')
        .then(res=>{
            setNumFzd(res.data.length)
        })
    },[])

    return (
        <div>
            <Header/>
            <Row style={{paddingTop:"20px"}} className="ml-2 justify-content-md-center" >
                <PropertyStats color="text-success" text={numProp} title="Proprietários Cadastrados"/>
                {/* <PropertyStats color="text-danger" text="57" title="Gerentes Cadatrados"/> */}
                <PropertyStats color="text-primary" text={numFzd} title="Fazendas Cadastradas"/>
                {/* <PropertyStats color="text-secondary" text="34" title="Chamados Enviados"/> */}
            </Row>
            <Row className="justify-content-md-center align-items-md-center" style={{height:"60px",marginTop:'30px'}}>
                <FilterProperty text="Listagem de proprietários" show="1"/>
            </Row>
            <Row className={"justify-content-md-center"} >
                <MapAndList/>
            </Row>

        </div>
    )
}

export default PropertyCadastraods
