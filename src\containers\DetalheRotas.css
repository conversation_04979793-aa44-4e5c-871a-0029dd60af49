
.cardRotas.card {
  padding: 10px;
  border-radius: 10px;
}

.card-componente.card {
  padding: 15px;
  width: 359px;
  height: 110px;
  border-radius: 10px;
  border:none;
}

.responsavel {
  position:absolute;
  font-family: 'Montserrat-Medium', 'Montserrat Medium', 'Montserrat', sans-serif;
  font-weight: 500;
  font-style: normal;
  font-size: 12px;
  color: #FFFFFF;
  text-align: center;
  line-height: 19px;
  right:0;
  bottom: 0;
  padding: 5px;
  border-top-left-radius: 10px;
  background-color: #027DB4;
}

.text-center-viatura-da-rota.card{
  border-radius: 10px;
  border:none;
}

.nome-componente.card-title {
  margin-bottom:0;
}

.row-viatura-por-rota.row {
  padding:10px;
}

.menuInfos {
  align-items: center;
  padding: 10px;
  border-radius:10px;
}

.menuInfos.rotas-cards-iniciais{
  align-items: flex-start;
  padding-top: 0;
  padding-bottom: 0;
}

.areaAtuacao-cidade {
  color:#027DB4;
  font-family: 'Montserrat-SemiBold', 'Montserrat SemiBold', 'Montserrat', sans-serif;
  font-weight: 650;
}

.img-componente.img-fluid {
  border-radius: 99%; 
  width: 80px;
  height: 80px;
}




