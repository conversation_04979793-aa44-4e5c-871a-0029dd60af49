import React, {useContext, useEffect, useState} from 'react';
import Header from '../components/Header'
import Footer from '../components/Footer'

import axios from 'axios';

import { 
    Container,
    Row, 
    Col,  
    Form, 
    But<PERSON>, 
    Spinner,
    Range
} from 'react-bootstrap'
import { URL } from '../services';
import Swal from 'sweetalert2';


function FormsNewArea() {

    const [load, setLoad] = useState(false);

    const [nome, setNome] = useState();
    const [longitude, setLongitude] = useState();
    const [latitude, setLatitude] = useState();
    const [referencia, setReferencia] = useState();
    const [raio, setRaio] = useState()

    // useEffect(()=>{
    //     axios.get(URL+'selecionar_todos_proprietarios')
    //     .then(res=>{
    //         console.log(res);
    //         for (let i = 0; i < res.data.length; i++) {
    //             //setOptions(res.data[i][1])
    //             setOptions(draft=>{
    //                 draft.push(res.data[i][1])
    //             })
    //         }
    //     })
    //     .catch(err=>console.log(err))
    // },[])
    //const data = Date
    function submit(event){
        event.preventDefault();
        setLoad(true);
        axios.post(URL+'salvar_area',{
            nome_da_area: nome,
            ponto_de_referencia: referencia,
            raio: raio,
            latitude: latitude,
            longitude: longitude
        })
        .then(res=>{
            setLoad(false);
            Swal.fire(nome,'cadastrada com sucesso!','success')
        })
        .catch(err=>{
            setLoad(false)  
        })
    }
  
  return (
    <>
            <Form className="" onSubmit={submit}>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Nome da Área <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setNome(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Ponto de Referência <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setReferencia(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Latitude do centro da área <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setLatitude(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Longitude do centro da área <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setLongitude(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Raio de cobertura<font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="number" placeholder="Digite aqui" onChange={e => setRaio(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                </Row>
                <div class="col text-center mt-3">
                    <Button className='blueColor btnCadastro' type="submit" size='lg'>
                        {load 
                            ? 
                            <Spinner animation="border" />
                            :
                            'Criar Área'
                        }
                    </Button>
                </div>
            </Form>
    </>
  );
}

export default FormsNewArea;