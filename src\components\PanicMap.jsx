import React, {useContext, useEffect} from 'react';
import Footer from '../components/Footer'
import Map from '../components/Mapa';
import Header from '../components/Header';

import { Container, Row, Col, Card, Spinner } from 'react-bootstrap'


function PanicMap() {
    return (
<Card className='cardMap shadow' style={{width:'420px'}} id='cardMap'>
{/* <Card.Header className='cardMapHeader'>
  Resumo por Área
</Card.Header> */}
<h5 className='mt-4 px-3 mb-3 text-dark'>Resumo por Área</h5>
    <Map height={650} largura={"200"}/>
    <div className='mx-auto menu'>
    
    </div>
</Card>)}
 
      

export default PanicMap

