# Deployment Guide - Coolify

Este documento descreve como fazer o deploy da aplicação AIBA no Coolify.

## Arquivos de Configuração

### Dockerfile
- **Multi-stage build** otimizado para produção
- **Build dependencies** incluídas para compilar módulos nativos (bcrypt, etc.)
- **Nginx** configurado para servir a aplicação React
- **Healthcheck** integrado para monitoramento

### nginx.conf
- Configuração otimizada para aplicações React (SPA)
- Suporte ao React Router com `try_files`
- Compressão gzip habilitada
- Cache de assets estáticos
- Headers de segurança
- Endpoint `/health` para healthcheck

### .dockerignore
- Exclui arquivos desnecessários do contexto de build
- Reduz o tamanho da imagem e acelera o build

## Configurações do Coolify

### Variáveis de Ambiente
Configure as seguintes variáveis no Coolify se necessário:

```bash
# Exemplo de variáveis que podem ser necessárias
REACT_APP_API_URL=https://api.exemplo.com
REACT_APP_ENV=production
```

### Recursos Recomendados
- **CPU**: 1-2 cores
- **RAM**: 2-4 GB (principalmente para o build)
- **Storage**: 1-2 GB

### Portas
- **Porta exposta**: 80
- **Protocolo**: HTTP

## Troubleshooting

### Erro de Memória durante Build
Se o build falhar por falta de memória, aumente os recursos do container ou ajuste o `--max-old-space-size` no Dockerfile.

### Problemas com bcrypt
O Dockerfile já inclui as dependências necessárias para compilar o bcrypt:
- python3
- make
- g++
- libc6-compat

### React Router não funciona
O nginx.conf já está configurado com `try_files $uri $uri/ /index.html;` para suportar o React Router.

## Monitoramento

### Healthcheck
- **Endpoint**: `/health`
- **Intervalo**: 30 segundos
- **Timeout**: 3 segundos
- **Retries**: 3

### Logs
Para visualizar logs no Coolify:
```bash
# Logs do nginx
docker logs <container_id>

# Logs de acesso do nginx
docker exec <container_id> tail -f /var/log/nginx/access.log

# Logs de erro do nginx
docker exec <container_id> tail -f /var/log/nginx/error.log
```

## Otimizações

### Cache
- Assets estáticos são cacheados por 1 ano
- Compressão gzip habilitada para reduzir tamanho

### Segurança
- Headers de segurança configurados
- Configuração otimizada do nginx

### Performance
- Build otimizado com multi-stage
- Imagem final baseada em Alpine (menor tamanho)
- Compressão de assets habilitada
