# Deployment Guide - Coolify (CPU Optimized)

Este documento descreve como fazer o deploy da aplicação AIBA no Coolify com otimizações para reduzir o uso de CPU durante o build.

## 🚀 Quick Start - Recommended Approach

Para servidores com recursos limitados, use o **Dockerfile.minimal**:

```bash
# No Coolify, configure para usar:
# Dockerfile: Dockerfile.minimal
# Build Command: (deixe vazio - usa o padrão)
```

## 📁 Opções de Dockerfile

### 1. Dockerfile.minimal (RECOMENDADO)
- **Uso**: Servidores com poucos recursos
- **Node.js**: 16 (melhor compatibilidade, sem OpenSSL legacy)
- **Compilação**: Mínima (evita bcrypt compilation)
- **Memória**: 1GB máximo
- **CPU**: Uso mínimo

### 2. Dockerfile.prebuilt
- **Uso**: Servidores médios
- **Node.js**: 18 com binários pré-compilados
- **Compilação**: Reduzida (usa binários quando possível)
- **Memória**: 1.5GB máximo
- **CPU**: Uso moderado

### 3. Dockerfile (Original Otimizado)
- **Uso**: Servidores com mais recursos
- **Node.js**: 18 com compilação completa
- **Compilação**: Controlada (jobs=1, maxsockets=1)
- **Memória**: 2GB máximo
- **CPU**: Uso controlado

## 🔧 Otimizações Implementadas

### Redução de CPU Usage
1. **Limitação de Jobs**: `npm_config_jobs=1` e `maxsockets=1`
2. **Compilação Sequencial**: Evita paralelização que sobrecarrega CPU
3. **Binários Pré-compilados**: Usa binários quando disponível
4. **Node.js 16**: Evita problemas de compatibilidade que forçam recompilação

### Redução de Memória
1. **Heap Size Reduzido**: De 4GB para 1-2GB dependendo do Dockerfile
2. **npm ci**: Mais eficiente que `npm install`
3. **Instalação Separada**: Produção e desenvolvimento instalados separadamente
4. **Cache Otimizado**: Configurações de cache mais eficientes

### Configurações de Build
- **.npmrc**: Configurações globais para reduzir overhead
- **Build Args**: Argumentos configuráveis para controle de recursos
- **Multi-stage**: Imagem final otimizada sem dependências de build

## 📋 Configuração no Coolify

### Opção 1: Dockerfile.minimal (Recomendado)
```
Dockerfile: Dockerfile.minimal
Build Command: (vazio)
Port: 80
Health Check: /
```

### Opção 2: Com Build Script Otimizado
```
Dockerfile: Dockerfile.minimal
Build Command: chmod +x build-optimized.sh && ./build-optimized.sh
Port: 80
Health Check: /health
```

## Arquivos de Configuração

### Dockerfile
- **Multi-stage build** otimizado para produção
- **Build dependencies** incluídas para compilar módulos nativos (bcrypt, etc.)
- **Nginx** configurado para servir a aplicação React
- **Healthcheck** integrado para monitoramento

### nginx.conf
- Configuração otimizada para aplicações React (SPA)
- Suporte ao React Router com `try_files`
- Compressão gzip habilitada
- Cache de assets estáticos
- Headers de segurança
- Endpoint `/health` para healthcheck

### .dockerignore
- Exclui arquivos desnecessários do contexto de build
- Reduz o tamanho da imagem e acelera o build

## Configurações do Coolify

### Variáveis de Ambiente
Configure as seguintes variáveis no Coolify se necessário:

```bash
# Exemplo de variáveis que podem ser necessárias
REACT_APP_API_URL=https://api.exemplo.com
REACT_APP_ENV=production
```

### Recursos Recomendados
- **CPU**: 1-2 cores
- **RAM**: 2-4 GB (principalmente para o build)
- **Storage**: 1-2 GB

### Portas
- **Porta exposta**: 80
- **Protocolo**: HTTP

## 🔧 Troubleshooting

### ⚠️ Servidor Travando Durante Build
**Problema**: CPU 100%, servidor não responde
**Soluções**:
1. Use `Dockerfile.minimal` (Node 16, sem compilação)
2. Configure limites de recursos no Coolify
3. Use build em horários de menor uso
4. Considere build em servidor separado

### 💾 Erro de Memória durante Build
**Problema**: "JavaScript heap out of memory"
**Soluções**:
```bash
# Opção 1: Reduzir heap size
ENV NODE_OPTIONS="--max-old-space-size=1024"

# Opção 2: Usar Dockerfile.minimal
# Já configurado com uso mínimo de memória
```

### 🔨 Problemas com bcrypt/Compilação
**Problema**: Falha na compilação de módulos nativos
**Soluções**:
1. **Dockerfile.minimal**: Evita compilação (RECOMENDADO)
2. **Dockerfile.prebuilt**: Usa binários pré-compilados
3. **Original**: Inclui todas as dependências de build

### 🌐 React Router não funciona
**Problema**: Rotas retornam 404
**Solução**: nginx.conf já configurado com `try_files $uri $uri/ /index.html;`

### 🐌 Build Muito Lento
**Problema**: Build demora mais de 10 minutos
**Soluções**:
1. Use `Dockerfile.minimal` (mais rápido)
2. Configure `.npmrc` para cache otimizado
3. Use `npm ci` ao invés de `npm install`
4. Desabilite audit e fund: `--no-audit --no-fund`

### 📊 Monitoramento de Recursos
Para monitorar durante o build:
```bash
# CPU usage
top -p $(pgrep docker)

# Memory usage
docker stats

# Build logs
docker logs -f <container_id>
```

## Monitoramento

### Healthcheck
- **Endpoint**: `/health`
- **Intervalo**: 30 segundos
- **Timeout**: 3 segundos
- **Retries**: 3

### Logs
Para visualizar logs no Coolify:
```bash
# Logs do nginx
docker logs <container_id>

# Logs de acesso do nginx
docker exec <container_id> tail -f /var/log/nginx/access.log

# Logs de erro do nginx
docker exec <container_id> tail -f /var/log/nginx/error.log
```

## Otimizações

### Cache
- Assets estáticos são cacheados por 1 ano
- Compressão gzip habilitada para reduzir tamanho

### Segurança
- Headers de segurança configurados
- Configuração otimizada do nginx

### Performance
- Build otimizado com multi-stage
- Imagem final baseada em Alpine (menor tamanho)
- Compressão de assets habilitada
