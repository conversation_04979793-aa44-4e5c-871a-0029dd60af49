import React, { useState, useEffect } from 'react';
import { IoClose } from "react-icons/io5"
import { Modal, Form, Button, Row, Col, Spinner } from 'react-bootstrap'
import axios from 'axios';
import { URL_API } from '../../../services';

function FormsUpdateArea({open, onHide, data, update}) {
  const [load, setLoad] = useState(false)

  const [inputs, setInputs] = useState(data)

  useEffect(() => {
    setInputs(data)
  },[data])

  const handleInputChange = e => {
    setInputs(prev => {
      return { ...prev, [e.target.name]: e.target.value }
    })
  }

  const submit = async (e) => {
    e.preventDefault()
    const dataToSend = {
      id: data.id,
      nome_da_area: inputs.nome_da_area,
      ponto_de_referencia: inputs.ponto_de_referencia,
      latitude: inputs.latitude,
      longitude: inputs.longitude,
      raio: inputs.raio,
      ativa_inativa: data.ativa_inativa,
    }

    const response = await axios.put(URL_API+"atualizar_area", dataToSend)

    if(response.data == "OK") {
      update()
    }
    onHide()
  }

  return (
    <>
    <Modal
      show={open}
      onHide={onHide}
      aria-labelledby="example-modal-sizes-title-lg"
      contentClassName='dialogModal'
    >
      <Modal.Header>
        <Modal.Title>
          <h5 style={{fontWeight:'bolder'}}>Editar área de atuação</h5>
        </Modal.Title>
        <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={onHide} style={{fontSize:25, color:'#868686'}}/></Button>
      </Modal.Header>
      <Modal.Body>

      <Form className="" onSubmit={submit}>
              <Row className="g-2">
                  <Col md>
                      <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                          <Form.Label className='customLabel'>Nome da Área <font className='obr'>*</font></Form.Label>
                          <Form.Control 
                            className='customInput shadow' 
                            type="text" 
                            placeholder="Digite aqui" 
                            onChange={handleInputChange} 
                            required
                            name="nome_da_area"
                            value={inputs.nome_da_area}
                          />
                      </Form.Group>
                  </Col>
                  <Col md>
                      <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                          <Form.Label className='customLabel'>Ponto de Referência <font className='obr'>*</font></Form.Label>
                          <Form.Control 
                            className='customInput shadow' 
                            type="text" 
                            placeholder="Digite aqui"
                            name="ponto_de_referencia"
                            value={inputs.ponto_de_referencia}
                            onChange={handleInputChange} 
                            required
                          />
                      </Form.Group>
                  </Col>
              </Row>
              <Row className="g-2">
                  <Col md>
                      <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                          <Form.Label className='customLabel'>Latitude do centro da área <font className='obr'>*</font></Form.Label>
                          <Form.Control 
                            className='customInput shadow' 
                            type="text" 
                            placeholder="Digite aqui" 
                            name="latitude"
                            onChange={handleInputChange} 
                            required
                            value={inputs.latitude}
                          />
                      </Form.Group>
                  </Col>
                  <Col md>
                      <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                          <Form.Label className='customLabel'>Longitude do centro da área <font className='obr'>*</font></Form.Label>
                          <Form.Control 
                          className='customInput shadow' 
                          type="text" 
                          placeholder="Digite aqui" 
                          onChange={handleInputChange} 
                          required
                          name="longitude"
                          value={inputs.longitude}
                          />
                      </Form.Group>
                  </Col>
              </Row>
              <Row className="g-2">
                  <Col>
                      <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                          <Form.Label className='customLabel'>Raio de cobertura<font className='obr'>*</font></Form.Label>
                          <Form.Control 
                          className='customInput shadow' 
                          type="text" 
                          placeholder="Digite aqui" 
                          onChange={handleInputChange} 
                          required
                          name="raio"
                          value={inputs.raio}
                          />
                      </Form.Group>
                  </Col>
              </Row>
              <div class="col text-center mt-3">
                  <Button className='blueColor btnCadastro' type="submit" size='lg'>
                      {load 
                          ? 
                          <Spinner animation="border" />
                          :
                          'Editar Área'
                      }
                  </Button>
              </div>
          </Form>
      </Modal.Body>
    </Modal>
  </>
  );
}

export default FormsUpdateArea;