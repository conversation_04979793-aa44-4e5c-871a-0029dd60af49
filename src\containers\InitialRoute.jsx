import React,{useState,useEffect} from 'react'
import Header from '../components/Header'
import { Button, Row, Col, Container, Modal, Form, Spinner,FormControl} from 'react-bootstrap'
import InputMask from 'react-input-mask';
import { Typeahead } from 'react-bootstrap-typeahead';
import BasicMap from '../components/BasicMap'
import { useImmer } from 'use-immer';
import axios from "axios";
import {URL} from '../services/index';
import {useHistory, useParams} from 'react-router-dom';


function InitialRoute() {

    const { areaId, rotaId } = useParams();
    const history = useHistory();

    const submit=(e)=>{
        e.preventDefault();
        if(prop && date){
            localStorage.setItem("initialroute",JSON.stringify({id:prop,date:date}))
            history.push(`/InitialRoute2/${areaId}/${rotaId}/${prop}/${date}`)
        }
        
    }

    useEffect(() => {
        const identificacao = localStorage.getItem("@Ident");
        if(identificacao){
            if(!(identificacao !== "COORDPM" || identificacao != "ADMINPM")){
                window.location.href="/PoliceRoute";
            }
        }
    }, [])
    


    const [options, setOptions] = useImmer([]);
    const [prop, setProp] = useState(null);
    const [date, setDate] = useState(null);
    useEffect(()=>{
        localStorage.setItem("initialroute","")
        axios.get(URL+'todos_policiais')
        .then(res=>{
            console.log(res.data)
            for (let i = 0; i < res.data.length; i++) {
                const optionsOpRequest = res.data.map((item)=>({
                    id: item.id,
                    name: item.nome
                }))
                setOptions(optionsOpRequest)
            }
        })
        .catch(err=>console.log(err))
    },[])

    useEffect(() => {
        console.log(prop)
    }, [prop])
    useEffect(() => {
        console.log(date)
    }, [date])



    return (
        <>
          <Header/>
          <Row style={{width:'100%',height:'100vh',minWidth:'1100px'}}>
              <div style={{width:'40%',display:'flex',justifyContent:'flex-end',paddingTop:'30px' }}>
                    <BasicMap areaRota={{Fazenda: 7,
Viatura: 2,
id: 1,
latitude: "-23.74026413163129",
longitude: "-46.6215440391756",
nome_da_area: "Mata",
ponto_de_referencia: "Acampamento dos Engenheiros",
raio: "50"
}}/>
              </div>
              <div style={{display:'flex',flexDirection:'column',width:'60%',paddingTop:'30px',paddingLeft:'30px',height:'100vh',overflow:'auto'}}>
                <Form onSubmit={submit}>  
                    <Row style={{width:'70%',marginTop:"20px",display:"flex",height:'100%'}}>
                        <h4 style={{fontFamily:'Montserrat'}}>Insira as informações de início da rota</h4>

                        <Form.Group className="mb-4 mt-4" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Data de início <font className='obr'>*</font></Form.Label>
                                <Form.Control className='customInput shadow' type="date"  placeholder="Digite aqui" onChange={e => setDate(e.target.value)} required/>
                        </Form.Group>
                        <Form.Group className="mb-4 mt-3" controlId="formBasicPassword" hasValidation>
                                <Form.Label className='customLabel'>Escolha o Responsável da Rota <font className='obr'>*</font></Form.Label>
                                <Typeahead
                                    id="basic-typeahead-single"
                                    labelKey="name"
                                    onChange={selected=>{
                                        //setProp(selected.replace('"','').replace('[',''))
                                        setProp(selected.length>0?selected[0].id:null)
                                    }}
                                    inputProps={{ required: true }}
                                    options={options}
                                    placeholder="Pesquisar..."
                                    className='customInput shadow'
                                />
                        </Form.Group>
                        <Form.Group style={{width:'100%',display:'flex',justifyContent:'flex-end'}} className="mt-3"  controlId="formBasicPassword" hasValidation>
                                <Form.Control style={{width:'200px'}} value="Prosseguir" className='customInput mt-5 shadow' type="submit" placeholder="Digite aqui" />
                        </Form.Group>
                    </Row>
                </Form>
              </div>
          </Row>

        </>
    )
}

export default InitialRoute
