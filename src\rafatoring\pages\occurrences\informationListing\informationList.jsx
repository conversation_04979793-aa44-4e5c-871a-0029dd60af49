import axios from 'axios';
import React, { useState, useEffect } from 'react';
import { URL_API } from '../../../../services/index';
import { TableComponent } from '../../../components/organisms';
import { TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import InformationListingSingle from './informationListingSingle';

function InformationList(){

    const [informations, setInformations] = useState([]);
    const [reasons, setReasons] = useState([]);
    const [farms, setFarms] = useState([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
   
    async function fetchDataInformations(){
       
        const response = await axios.get(URL_API+"informacoes");
        return response.data.message ? [] : response.data;
    }

    async function fetchDataReasons(){
        const response  = await axios.get(URL_API+"motivos_informacao")
        return response.data
    }

   
    async function fetchDataFarms(){
        const response = await axios.get(URL_API+"todas_fazendas") //utilizar no lugar do enpoint todos os proprietários, nome proprietario
        return response.data   
    }

    //Promisse.all, garante que o then só será executado depois que todas as promises forem concluidas.
    useEffect(async () => {
        Promise.all([
            await fetchDataInformations(),
            await fetchDataReasons(),
            await fetchDataFarms(),
        ]).then((result) => {
            const [
                informations,
                reasons,
                farms,
            ] = result;
            setInformations(informations);
            setReasons(reasons);
            setFarms(farms);
            
        })  
    }, [])
    

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    return (
        <>
            {informations
                ? 
                <TableComponent
                    count={informations.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Informações Registradas'
                >
                        <TableHead>
                            <TableRow>
                                <TableCell>Data/Hora</TableCell>
                                <TableCell align='center'>Fazenda/Proprietário</TableCell>
                                <TableCell align='center'>Motivo</TableCell>
                                <TableCell></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {informations.length == 0 
                                ?
                                <p style={{padding:'10px'}}>Nenhum dado encontrado</p>
                                :
                                <InformationListingSingle informations={informations} page={page} rowsPerPage={rowsPerPage} farms={farms} reasons={reasons}/>
                            }
                        </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>
    )
}

export default InformationList;
