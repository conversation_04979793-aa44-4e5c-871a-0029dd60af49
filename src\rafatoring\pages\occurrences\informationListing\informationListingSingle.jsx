import React from 'react'
import { TableRow, TableCell, MenuItem, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../../components/organisms';
import { BiDetail } from 'react-icons/bi';

function InformationListingSingle({informations, page, rowsPerPage, farms, reasons}) {

    const getDate = (date) => {
        let dateSplit = date.split("T");
        let dateNewFormat = dateSplit[0].split("-");
        return {date:`${dateNewFormat[2]}-${dateNewFormat[1]}-${dateNewFormat[0]}`, hour: dateSplit[1].split('.')[0]}
    }

    const getFarmById = (FarmId) => {
        let farmFilter = farms.filter((farm) => {
            return farm.id_fzd == FarmId
        })

        return {nameFarm: farmFilter[0]?.nome_da_fazenda, nameOwner: farmFilter[0]?.nome_do_proprietario};
    }

    const getReasonById = (reasonId) => {
        let reasonFilter = reasons.filter((reason) => {
            return reason.id == reasonId
        })
        return reasonFilter[0]?.descricao;
    }



    return (
      <>
        {informations?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index)=>(
            <TableRow key={index}>
                <TableCell style={{ minWidth:'150px'}}>
                    <h6 style={{fontWeight: 'bold'}}>{getDate(i.created_at).date}</h6>
                    <h6>{getDate(i.created_at).hour}</h6>
                </TableCell>
                <TableCell align='center' style={{ minWidth:'150px'}}>
                    <h6 style={{fontWeight: 'bold'}}>{getFarmById(i.id_fzd).nameFarm}</h6>
                    <h6>{getFarmById(i.id_fzd).nameOwner}</h6>
                </TableCell>
                <TableCell align='center'>
                    <h6>{getReasonById(i.id_motivo)}</h6>
                </TableCell>
                <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem>
                            <ListItemIcon><BiDetail size={20}/></ListItemIcon>
                            Detalhes da Informação
                        </MenuItem>
                    </TableItemMenuMulti>
                </TableCell>
            </TableRow>
            
        ))}
      </>
    )
}

export default InformationListingSingle;
