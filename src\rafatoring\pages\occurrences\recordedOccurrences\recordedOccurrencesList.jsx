import axios from 'axios';
import React, { useState, useEffect } from 'react';
import { URL_API } from '../../../../services/index';
import SelectStatus from './SelectStatus';
import { TableComponent } from '../../../components/organisms';
import { TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import RecordedOccurrencesSingle from './recordedOccurrencesSingle';

function RecordedOccurrencesList() {

    const [occurrences, setOccurrences] = useState([]);
    const [reasons, setReasons] = useState([]);
    const [farms, setFarms] = useState([]);
    const [status, setStatus] = useState('Concluída');
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
   
    async function getOccurrences(){
        let params = '';

        if(status != ''){
            params =`?status=${status}`
        }
        const response = await axios.get(URL_API+'ocorrencias'+params)
        
        return response.data.message ? [] : response.data;
    }

    async function getReasons(){
        const response  = await axios.get(URL_API+"motivos_ocorrencia")
        return response.data.Motivos
    }

   
    async function getFarms(){
        const response = await axios.get(URL_API+"todas_fazendas")
        return response.data   
    }


    const refresh = async () => {
        const occurrences = await getOccurrences();
        setOccurrences(occurrences);
    }

    //Promisse.all, garante que o then só será executado depois que todas as promises forem concluidas.
    useEffect(async () => {
        Promise.all([
            await getOccurrences(),
            await getReasons(),
            await getFarms(),
        ]).then((result) => {
            const [
                occurrences,
                reasons,
                farms,
            ] = result;
            
            setReasons(reasons);
            setFarms(farms);
            
        } )  
    }, [])
    

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    useEffect(()=>{
        refresh()
    },[status])

    return (
        <>
            {occurrences
                ? 
                <TableComponent 
                    count={occurrences.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Ocorrências Registradas'
                    subtitle={<SelectStatus status={status} setStatus={(e) => setStatus(e)}/>}
                >
                        <TableHead>
                            <TableRow>
                                <TableCell>Data/Hora</TableCell>
                                <TableCell align='center'>Fazenda/Proprietário</TableCell>
                                <TableCell align='center'>Tipo de Ocorrência</TableCell>
                                <TableCell align='center'>Viatura/Componentes</TableCell>
                                <TableCell align='center'>Status</TableCell>
                                <TableCell></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {occurrences.length == 0 
                                ?
                                <p style={{padding:'10px'}}>Nenhum dado encontrado</p>
                                :
                                <RecordedOccurrencesSingle occurrences={occurrences} page={page} rowsPerPage={rowsPerPage} farms={farms} reasons={reasons} refresh={getOccurrences}/>
                            }
                        </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>
    )
}

export default RecordedOccurrencesList;
