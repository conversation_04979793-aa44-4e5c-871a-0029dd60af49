import React, {useEffect, useState} from 'react';
import Footer from '../components/Footer'
import Header from '../components/Header'
import { Container,Row, Col, Card, Image, ListGroup, CardGroup } from 'react-bootstrap'
import './Reports.css';
import axios from 'axios';
import { URL_API } from '../services';
import Swal from 'sweetalert2';

function Reports() {
  
  const [unidade, setUnidade] = useState();
  const [initialDate, setInitalDate] = useState();
  const [finalDate, setFinalDate] = useState();
  const [areaId, setAreaId] =  useState()
  const [areasAtuacao, setAreasAtuacao] = useState([]);
  const [reports, setReports] = useState([])
  const [errmessageInitial, setErrMessageInitial] = useState(false);
  const [errmessageFinal, setErrMessageFinal] = useState(false);
  
  const fetchTodasAreas = async () => { 
    const response = await axios.get(URL_API+"todas_areas")
    setAreasAtuacao(response.data)
  }

  const fetchRelatoriosRotas = async () => {

    let parametroAreaId = ''
    let parametroUnidade = '' 

    if(!initialDate){
      setErrMessageInitial(true);
      return
    }
    
    if(!finalDate){
      setErrMessageFinal(true);
      return
    }

    if(areaId){
      parametroAreaId = `&id_area=${areaId}`
    }

    if(areaId === 'areaAtuacao'){
      parametroAreaId = '';
    }

    if(unidade){
      parametroUnidade = `&unidade=${unidade}`
    }

    const response = await axios.get(URL_API+`relatorio_rotas?data_inicio=${initialDate}&data_final=${finalDate}` + parametroAreaId + parametroUnidade)
    
    .then(res=>{
      setReports(res.data)
      Swal.fire('Pesquisa realizada com sucesso.')
    })
    
    .catch(err=>{
      console.log("Erro", err)
    })
   
  }

  useEffect(() => {
    fetchTodasAreas()
  }, [])

  
  return (
    <>
      <Header />
        <Container className="mt-5"> 
          <Row style={{marginBottom:'20px'}}>
            <Col style={{display:"flex", flexDirection:'column'}}>
              <span>Data inicial</span>
              <input type="date" required value={initialDate} onChange={e=>setInitalDate(e.target.value)}/>
              { 
                (errmessageInitial && !initialDate ) && <span className='errmessage'>Campo obrigatório</span> 
              }  
            </Col>
            <Col style={{display:"flex", flexDirection:'column'}}>
              <span>Data final</span>
              <input type="date" required value={finalDate} onChange={e=>setFinalDate(e.target.value)}/>
              { 
                (errmessageFinal && !finalDate) && <span className='errmessage'>Campo obrigatório</span>
              }
            </Col>
            <Col style={{display:"flex", flexDirection:'column'}}>
              <span>Área de atuação</span>
              <select className='select' value={areaId} onChange={(e, i)=> setAreaId(e.target.value)}>
                <option value={'areaAtuacao'}>Selecione a área de atuação:</option>
                {areasAtuacao.map((area, i) => {
                  return (
                    <option value={area.id}>{area.nome_da_area}</option>
                  )
                })}
              </select>
            </Col>
            <Col style={{display:"flex", flexDirection:'column'}}>
              <span>Unidade</span>
              <select className='select' value={unidade} onChange={e=>setUnidade(e.target.value)}>
                <option value=''>Selecione a unidade:</option>
                <option value='CPRO'>CPRO</option>
                <option value='CIPT-O'>CIPT-O</option>    
                <option value='85a. CIPM'>85a. CIPM</option>    
                <option value='86a. CIPM'>86a. CIPM</option>    
                <option value='CIPRv'>CIPRv</option>    
                <option value='CIPE CERRADO'>CIPE CERRADO</option> 
              </select>
            </Col>
            <Col style={{display:"flex", flexDirection:'column', justifyContent:'flex-end'}}>
              <button className='search'
                type="submit"
                onClick={fetchRelatoriosRotas}
              >
                Pesquisar
              </button>
            </Col>
          </Row>  
          <Row>
            <Col>
              <Card className='menuInfos rotas-cards-iniciais shadow'>
                <div className='contentInfo'>
                  Pessoas abordadas
                  <div className='contentInfoNumber'>
                    {reports[0]? reports[0].pessoas_abordadas : 0}
                  </div>
                </div>
              </Card>
              <Card className='menuInfos rotas-cards-iniciais shadow'>
                <div className='contentInfo'>
                  Armas apreendidas
                  <div className='contentInfoNumber'>
                    {reports[0]? reports[0].armas_apreendidas : 0}
                  </div>
                </div>
              </Card>
              <Card className='menuInfos rotas-cards-iniciais shadow'>
                <div className='contentInfo'>
                  Carga recuperada
                  <div className='contentInfoNumber'>
                    {reports[0]? reports[0].carga_recuperada : 0}
                  </div>
                </div>
              </Card>
            </Col>
            <Col >
              <Card className='menuInfos rotas-cards-iniciais shadow'>
                <div className='contentInfo'>
                  Veículos 4 rodas abordados
                  <div className='contentAtivNumber'>
                    {reports[0]? reports[0].veiculos_4_rodas : 0}
                  </div>                           
                </div>
              </Card>
              <Card className='menuInfos rotas-cards-iniciais shadow'>
                <div className='contentInfo'>
                  Veículo recuperado
                  <div className='contentAtivNumber'>
                    {reports[0]? reports[0].veiculo_recuperado : 0}
                  </div>                           
                </div>
              </Card>
            </Col>
            <Col>
              <Card className='menuInfos rotas-cards-iniciais shadow'>
                <div className='contentInfo'>
                  Veículos 2 rodas abordados
                  <div className='contentInativNumber'>
                    {reports[0]? reports[0].veiculos_2_rodas : 0}
                  </div>
                </div>
              </Card>
              <Card className='menuInfos rotas-cards-iniciais shadow'>
                <div className='contentInfo'>
                  Estabelecimento abordado
                  <div className='contentInativNumber'>
                    {reports[0]? reports[0].estabelecimento_abordado : 0}
                  </div>
                </div>
              </Card>
            </Col>
            <Col>
            </Col>
            <Col>
            </Col>
          </Row>
        </Container>
      <Footer/>
    </>
  );
}

export default Reports;