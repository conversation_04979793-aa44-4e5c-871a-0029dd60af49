
.rotas-fazendas-card.card {
  padding-top: 15px;
  display:flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(240, 246, 249, 1);
  border:none;
}

.nome-da-fazenda.card-title{
  margin-bottom:0;
}

.fazenda-proprietario.card-body {
 display:block;
 align-items: center;
}

.card-text {
  align-items: center;
}

.latidude.card-text {
  margin-bottom:0px;
}

.rotas-fazendas-img.card-img {
  border-radius: 99%;
  max-width: 80px; 
}

.rotas-fazendas-card-tooltip {
  position:absolute;
  right:10px;
  width: 10px;
  height: 10px;
  border-radius: 3px;
  background-color:  rgba(0, 118, 193, 1);

}

.rotas-fazendas-card-tooltip.visitado {
  background-color:  rgba(0, 169, 86, 1);
}

.Icon_pernoite {
  margin-left: 10px;
  
}

.fazenda-proprietario.card-body{
  padding-bottom: 0;
}

.ajusteicones{
  width:100px;
  height:40px;
}

.list-group-flush.card-body{
  padding-top:0px;
}

