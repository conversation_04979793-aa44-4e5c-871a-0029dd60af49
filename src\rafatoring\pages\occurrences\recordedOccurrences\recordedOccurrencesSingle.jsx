import React,{useState, useEffect} from 'react'
import MenuIcon from '@material-ui/icons/Menu';
import { Menu } from '@material-ui/core';
import PanicOptions from '../../../../components/PanicOptions';
import ReactTooltip from 'react-tooltip';
import { TableRow, TableCell, MenuItem, ListItemIcon } from '@mui/material';
import { TableItemMenuMulti } from '../../../components/organisms';
import { useHistory } from 'react-router-dom';
import { BiDetail } from "react-icons/bi";
import { FaExchangeAlt } from "react-icons/fa";
import { ImCancelCircle } from "react-icons/im";
import ModalRedirecionarOcorrencia from '../../../../components/ModalRedirecionarOcorrencia';
import ModalCancelarOcorrencia from '../../../../components/ModalCancelarOcorrencia';
import CustomButtonCriar from '../../../../components/CustomButtonCriar';

function RecordedOccurrencesSingle({occurrences, page, rowsPerPage, farms, reasons, refresh}) {

    const person = window.localStorage.getItem('@Ident');

    const [modal, setModal] = useState({open: false, occurrence:{}, typeCancel: false});

    const getDate = (date) => {
        let dateSplit = date.split("T");
        let dateNewFormat = dateSplit[0].split("-");
        return {date:`${dateNewFormat[2]}-${dateNewFormat[1]}-${dateNewFormat[0]}`, hour: dateSplit[1].split('.')[0]}
    }

    const getFarmById = (FarmId) => {
        let farmFilter = farms.filter((farm) => {
            return farm.id_fzd == FarmId
        })

        return {nameFarm: farmFilter[0]?.nome_da_fazenda, nameOwner: farmFilter[0]?.nome_do_proprietario};
    }

    const getReasonById = (reasonId) => {
        let reasonFilter = reasons.filter((reason) => {
            return reason.id == reasonId
        })
        return reasonFilter[0]?.descricao;
    }

    const handleStatus = (status) => {
      if(status == 'Aguardando atendimento'){
        return "orange"
      }
      if(status == 'Concluída'){
        return "green";
      }
      if(status == "Em atendimento"){
        return "blue"
      }
      if(status == "Cancelada"){
        return "red"
      }
    }

    const handleClickRedirect = (occurrence) => {
        setModal({open: true, occurrence: occurrence, typeCancel: false});
    }

    const handleClickCancel = (occurrence) => {
        setModal({open: true, occurrence: occurrence, typeCancel: true});
    }

    return (
      <>
        {occurrences?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((i, index)=>(
            <TableRow key={index}>
                <TableCell style={{ minWidth:'150px'}}>
                    <h6 style={{fontWeight: 'bold'}}>{i.status == 'Concluída' ? getDate(i.updated_at).date : getDate(i.created_at).date}</h6>
                    <h6>{i.status == 'Concluída' ? getDate(i.updated_at).hour : getDate(i.created_at).hour}</h6>
                </TableCell>
                <TableCell align='center' style={{ minWidth:'150px'}}>
                    <h6 style={{fontWeight: 'bold'}}>{getFarmById(i.id_fzd).nameFarm}</h6>
                    <h6>{getFarmById(i.id_fzd).nameOwner}</h6>
                </TableCell>
                <TableCell align='center'>
                    <h6>{getReasonById(i.id_motivo)}</h6>
                </TableCell>
                <TableCell align='center'>
                    {i.status == 'Cancelada'
                        ?
                        i.status
                        :
                        i.status !== "Aguardando atendimento"
                            ?
                            `#${i.id_viat}`
                            :
                            person !== "COORDPM"
                                ?
                                i.status
                                :
                                <CustomButtonCriar
                                    tipo="selecionarViatura"
                                    idOcorrencia={i.id}
                                    fetchOcorrencias={refresh}
                                />

                    }
                </TableCell>
                <TableCell>
                    <ReactTooltip place="bottom" className="text" id={`tooltip-${i.id}`} aria-haspopup='true' type="dark" effect='solid'>            
                        <span>{i.status}</span>          
                    </ReactTooltip>
                    <div 
                        data-tip 
                        data-for={`tooltip-${i.id}`}
                        style={{
                            width:'10px',
                            height:'10px', 
                            backgroundColor: handleStatus(i.status),
                            borderRadius:'3px',
                            marginLeft: '15px'
                        }}>
                    </div>
                </TableCell>
                <TableCell>
                    <TableItemMenuMulti>
                        <MenuItem>
                            <ListItemIcon><BiDetail size={20}/></ListItemIcon>
                            Detalhes do Chamado
                        </MenuItem>
                    
                        {(i.status === 'Em atendimento' || i.status === 'Aguardando atendimento') &&
                            <MenuItem onClick={() => handleClickCancel(i)}>
                                <ListItemIcon><ImCancelCircle size={20}/></ListItemIcon>
                                Cancelar Ocorrência
                            </MenuItem>
                        }
                
                        {i.status === 'Em atendimento' &&
                            <MenuItem onClick={() => handleClickRedirect(i)}>
                                <ListItemIcon><FaExchangeAlt size={20}/></ListItemIcon>
                                Redirecionar Ocorrência
                            </MenuItem>
                        }
                    </TableItemMenuMulti>
                </TableCell>
            </TableRow>
            
        ))}
        {
            modal.open && modal.typeCancel &&
            <ModalCancelarOcorrencia 
                row={modal.occurrence}
                fetchOcorrencias={refresh}
            />
        }
        {
            modal.open && !modal.typeCancel &&
            <ModalRedirecionarOcorrencia 
                row={modal.occurrence}
                fetchOcorrencias={refresh}
            />
        }
      </>
    )
}

export default RecordedOccurrencesSingle;
