import React, {useState, useEffect} from 'react';
import Map from '../../../components/Mapa';

import { Container, Row, Col, Card, Spinner } from 'react-bootstrap'

import { URL_API } from '../../../services';
import CustomButton from '../../../components/CustomButton';
import CustomButtonCriar from '../../../components/CustomButtonCriar';

import axios from 'axios'


function VehicalMap() {

    const [areasAtuacao, setAreasAtuacao] = useState([]);

    const fetchTodasAreas = async () => { 
        const response = await axios.get(URL_API+"todas_areas")
        setAreasAtuacao(response.data)
    }

    useEffect(()=>{
        fetchTodasAreas();
    },[])
    return (
        <Card className='cardMap shadow' style={{width:'420px'}} id='cardMap'>
        {/* <Card.Header className='cardMapHeader'>
        Resumo por Área
        </Card.Header> */}
        <h5 className='mt-4 px-3 mb-3 text-dark'>Resumo por Área</h5>
            <Map areasAtuacao={areasAtuacao} height={650} largura={"200"}/>
            <div className='mx-auto menu'>
            <Row >
                <Col md='6' sm='6' lg='6'>
                    
                    <CustomButtonCriar tipo="viatura"/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                    <CustomButton tipo='fazendas'/>
                    </Col>
                </Row>
                <Row>
                    <Col md='6' sm='6' lg='6'>
                    <CustomButton tipo='rotas'/>
                    </Col>
                    <Col md='6' sm='6' lg='6'>
                    <CustomButton tipo='policiais'/>
                    </Col>
                </Row>
            </div>
            </Card>
    )}
 
      

export default VehicalMap

