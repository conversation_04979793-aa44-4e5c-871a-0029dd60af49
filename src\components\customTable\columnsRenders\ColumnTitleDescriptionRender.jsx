import React from 'react';
import './ColumnTitleDescriptionRender.css';



const ColumnTitleDescriptionRender = (name, title, description) =>{  
    return {
        name: name, //header do datatable   
            cell: (row) => {          
                return ( 
                    <div>
                        <div  className="Title">{row[title]}</div>
                        <div className="Description">{row[description]}</div>
                    </div>
                )
            }
    }
}

export default ColumnTitleDescriptionRender; 

