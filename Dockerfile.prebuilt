# Alternative Dockerfile using pre-built binaries to avoid compilation
# This version significantly reduces CPU usage during build

# Build Stage - Using standard Node image with pre-built binaries
FROM node:18-slim AS build

# Install only essential dependencies
RUN apt-get update && apt-get install -y \
    git \
    ca-certificates \
    --no-install-recommends && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy package files
COPY package*.json ./

# Configure npm for faster, less resource-intensive installs
RUN npm config set maxsockets 3 && \
    npm config set progress false && \
    npm config set audit false && \
    npm config set fund false

# Set Node.js options with conservative memory allocation
ENV NODE_OPTIONS="--openssl-legacy-provider --max-old-space-size=1536"

# Use npm ci for faster, deterministic installs
# Force use of pre-built binaries where possible
RUN npm ci --legacy-peer-deps --prefer-offline --no-audit --no-fund

COPY . .

# Build the application
RUN npm run build

# Production Stage - Optimized nginx
FROM nginx:stable-alpine AS production

# Install wget for healthcheck (minimal overhead)
RUN apk add --no-cache wget

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application
COPY --from=build /app/build /usr/share/nginx/html

# Copy and setup healthcheck
COPY healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Add healthcheck with longer intervals to reduce overhead
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
