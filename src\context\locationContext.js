import React, { useState, useContext,  useEffect } from 'react';
import Swal from 'sweetalert2'

const PositionContext = React.createContext();

export function usePositionContext() {
  return useContext(PositionContext);
}

export function PositionProvider({children}) {
    const [latitude, setLatitude] = useState();
    const [longitude, setLongitude] = useState();

    useEffect(()=>{
      if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(function(position) {
            setLatitude(position.coords.latitude);
            setLongitude(position.coords.longitude);
            window.localStorage.setItem('latitude', position.coords.latitude)
            window.localStorage.setItem('longitude', position.coords.longitude)
        });
      } 
      else  
      {
          alert("I'm sorry, but geolocation services are not supported by your browser.");
      }
    },[latitude, longitude])

    return (
        <PositionContext.Provider value={{latitude, longitude}}>
          {children}
        </PositionContext.Provider>
      );
}
