
import axios from 'axios';
import React from 'react';
import { Button, Card, ListGroup } from 'react-bootstrap';
import Swal from 'sweetalert2';
import { URL_API } from '../services';


const VehiclePush = ({data,idOcorrencia,fetchOcorrencias}) =>{

  const Push = ( ) => {
    axios.post(URL_API+"enviar_viatura",{
      id_viat: data.id_viat,
      id_ocorrencia: idOcorrencia,
    })

    .then(res=>{
      Swal.fire(data.modelo_viat ? data.modelo_viat : "",'Em andamento!','success')
      fetchOcorrencias()
    })
    .catch(err=>{
       
    })

  }
  
  return(
    <>
      <Card style={{ width: '100%', justifyContent:'Center', marginBottom:'20px'}}>
        <Card.Header>Modelo:{data.modelo_viat}</Card.Header>
        <ListGroup variant="flush">
        <ListGroup.Item>#{data.id_viat}</ListGroup.Item>
        <ListGroup.Item>Unidade:{data.unidade}</ListGroup.Item>
        <ListGroup.Item>Area de Atuação:{data.area_atuacao}</ListGroup.Item>
        </ListGroup>
        <Button onClick={()=> Push()} style={{ backgroundColor:' rgba(0, 118, 193, 1)'}}>Enviar Viatura</Button>
      </Card>
    </>
  )
}
export default VehiclePush;