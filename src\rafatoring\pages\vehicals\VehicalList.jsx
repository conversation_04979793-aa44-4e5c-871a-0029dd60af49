import React, {useState,useEffect} from 'react'
import {URL} from '../../../services/index'
import { useUserContext } from '../../../context/authContext';
import VehicalSingle from './VehicalSingle';
import { TableHead, TableRow, TableCell, TableBody } from '@mui/material';
import { TableComponent } from '../../components/organisms';

function VehicalList() {
    const {toggle,select,search1} = useUserContext();
    const [viaturas, setViaturas] = useState([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    
    useEffect(() => {
        async function fun(){
            var data=await fetch(`${URL}todas_viaturas`);
            data=await data.json();
            setViaturas(data.reverse());
        }
        fun()

    }, [toggle])//toggle

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
      };
    
    const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    };

    return (
        <>
            {viaturas
                ?
                <TableComponent 
                    count={viaturas.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Listagem de Viaturas'
                >
                    <TableHead>
                        <TableRow>
                            <TableCell></TableCell>
                            <TableCell align='center'>Viatura</TableCell>
                            <TableCell align='center'>Kms Rodados</TableCell>
                            <TableCell align='center'>Visitas</TableCell>
                            <TableCell align='center'>Status</TableCell>
                            <TableCell></TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        <VehicalSingle data={viaturas} page={page} rowsPerPage={rowsPerPage}/>
                    </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>
    )
}

export default VehicalList
