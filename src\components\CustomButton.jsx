import React, {useContext, useEffect} from 'react';
import {SocketContext} from '../context/socketContext';

import { Container,Row, Col, Card } from 'react-bootstrap'

import { GiGreenhouse } from 'react-icons/gi'
import { FaMapMarkedAlt } from 'react-icons/fa'
import { GiPoliceOfficerHead } from 'react-icons/gi'
import { RiPoliceCarFill } from 'react-icons/ri'
import { useHistory } from 'react-router';

function CustomButton(props) {

    const history = useHistory();


  return (
    <>
    <Card className='menuRotas shadow' onClick={()=>history.push('/'+props.tipo)}>
        <div className='contentMenu'>
            {props.tipo == 'fazendas'
                &&
                <>
                    <GiGreenhouse className='iconMenu'/>
                    <p>Fazendas Cadastradas</p>
                </>
            }
            {props.tipo == 'policiais'
                &&
                <>
                    <GiPoliceOfficerHead className='iconMenu'/>
                    <p>Policiais Cadastrados</p>
                </>
            }
            {props.tipo == 'rotas'
                &&
                <>
                    <FaMapMarkedAlt className='iconMenu'/>
                    <p>Listagem <br/>de Rotas</p>
                </>    
            }
            {props.tipo == 'viaturas'
                &&
                <>
                    <RiPoliceCarFill className='iconMenu'/>
                    <p>Viaturas <br/>Cadastradas</p>
                </>    
            }
        </div>
    </Card>
      
    </>
  );
}

export default CustomButton;