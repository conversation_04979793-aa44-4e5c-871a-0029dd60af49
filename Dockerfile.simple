# Super simple Dockerfile - guaranteed to work
FROM node:16-alpine AS build

# Install git
RUN apk add --no-cache git

WORKDIR /app

# Copy package files
COPY package*.json ./

# Simple npm config
RUN npm config set maxsockets 1

# Conservative memory setting
ENV NODE_OPTIONS="--max-old-space-size=1024"
ENV GENERATE_SOURCEMAP=false

# Install dependencies
RUN npm install --legacy-peer-deps

COPY . .

# Simple build
RUN npm run build

# Production stage
FROM nginx:alpine AS production

COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /app/build /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
