import React from 'react';
import './ColumnStatusOcorrenciaRender.css';
import ReactTooltip from 'react-tooltip';

const ColumnStatusOcorrenciaRender = (name) =>{ 
  
  let cancelada = "cancelada"; /* cinza */
  let concluida = "concluida";    /*verde */ 
  let aguardando_atendimento = "aguardando-atendimento";  /* azul */ 
  let em_atendimento = "em-atendimento"; /*amarelo*/
  let classe = " ";
  let texto = " ";
 

  return {
    name:name,
    cell:(row, index) => {
      if(row.status === 'Cancelada') {
        classe = cancelada
        texto = "Cancelada"  
      }
      else if (row.status === 'Concluída') {
        classe = concluida
         texto = "Concluída"
      }
      else if (row.status === 'Aguardando atendimento'){
        classe = aguardando_atendimento
        texto = "Aguardando atendimento"
      }
      else if (row.status === 'Em atendimento'){
        classe = em_atendimento
        texto = "Em atendimento"
      }
      
      return (          
        <div>                                
           <div data-tip data-for={`tooltip-${index}`} className={`column-status ${classe}`}></div>
            <ReactTooltip place="bottom" className="text" id={`tooltip-${index}`} aria-haspopup='true' type="dark" effect='solid'>            
            <span>{texto}</span>           
          </ReactTooltip>                                    
        </div>
      )
    }
  }
}

export default ColumnStatusOcorrenciaRender; 


