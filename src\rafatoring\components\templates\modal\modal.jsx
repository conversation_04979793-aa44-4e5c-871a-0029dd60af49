import React from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';
import { IoClose } from "react-icons/io5";
import './modal.scss';

function ModalComponent({show, setShow, title, children}) {

    return(
        <Modal
            show={show}
            onHide={() => setShow(false)}
            aria-labelledby="example-modal-sizes-title-lg"
            contentClassName='dialogModal'
        >
            <Modal.Header>
                <Modal.Title>{title}</Modal.Title>
                <Button variant='light' className='modal-buttonClose'>
                    <IoClose className='modal-buttonClose--icon' onClick={()=> setShow(false)}/>
                </Button>
            </Modal.Header>
            <Modal.Body>
                {children}
            </Modal.Body>
        </Modal>
    )
}
export default ModalComponent