import React from 'react';
import './ColumnTitleDescriptionRender.css';
import { NavDropdown } from 'react-bootstrap';
import ReorderIcon from '@material-ui/icons/Reorder';
import ArrowForwardIosIcon from '@material-ui/icons/ArrowForwardIos';


const ColumnNavDropdownRender = (id) => {
  const handleClick = (idRota) => {
    window.localStorage.setItem('@IdRota', idRota)
    window.location.href = '/detalherotas'
  }
  const handleEdit = (e) => {
    console.log(id[0])
    window.localStorage.setItem('@IdRota', e.target.value)
    // window.location.href="/PoliceRoutes"
  }

  return {
    id: id,
    cell: (row) => {
      return (
        <NavDropdown title={<ReorderIcon color="disabled" />} id="basic-nav-dropdown">
          <NavDropdown.Item >Ações</NavDropdown.Item>
          <NavDropdown.Divider />
          <NavDropdown.Item onClick={() => handleClick(row[id])}>{<ArrowForwardIosIcon />}Detalhes da Rota</NavDropdown.Item>
          <NavDropdown.Item onClick={(e) => handleEdit(e)}><ArrowForwardIosIcon />Editar</NavDropdown.Item>
        </NavDropdown>
      )
    }
  }
}

export default ColumnNavDropdownRender;