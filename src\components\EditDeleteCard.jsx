import React, { useState } from 'react'
import {Card, Row, Col, Button} from 'react-bootstrap'
import {Menu} from '@material-ui/core'
import EditIcon from '@material-ui/icons/Edit';
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';

function EditDeleteCard(props) {

    return (
        <>
            <Card style={{backgroundColor:'white',width:'230px',padding:'5px',height:'160px',border:'none'}}>
                <Card.Header style={{backgroundColor:'white'}}>
                    Ações
                </Card.Header>
                <Card.Body>
                    <div style={{display:'flex',flexDirection:'column'}}>
                        <button onClick={props.edit} style={{display:'flex',border:'None',backgroundColor:'white',color:'blue',textAlign:'start'}}>
                            <EditIcon  /><p style={{marginLeft:'10px'}}>Editar</p>
                        </button>
                        {props.del === 'Ativar' ? 
                        <button onClick={props.submit} style={{display:'flex',border:'None',backgroundColor:'white',color:'blue',textAlign:'start'}}>
                            <CheckIcon/><p style={{marginLeft:'10px'}}>{props.del}</p>
                        </button>
                        :
                        <button onClick={props.submit} style={{display:'flex',border:'None',backgroundColor:'white',color:'blue',textAlign:'start'}}>
                            <CloseIcon/><p style={{marginLeft:'10px'}}>{props.del}</p>
                        </button>
                        }
                         
                    </div>
                </Card.Body>
            </Card>    
        </>
    )
}

export default EditDeleteCard
