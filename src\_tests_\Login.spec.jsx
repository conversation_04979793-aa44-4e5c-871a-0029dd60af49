import React from 'react';
import {render, screen, waitFor, fireEvent} from "@testing-library/react";
import Login from '../components/Login';
import {UserProvider} from '../context/authContext';


describe("Testing Login", () => {
  it("show the Login", () => {
    render(<UserProvider><Login/></UserProvider>);

    const login = screen.getByText("Para baixar o aplicativo acesse:");

    expect(login).toBeTruthy();
  })

  it("A Label User deverá estar na tela", () => {
    render(<UserProvider><Login/></UserProvider>);

    const user = screen.getByLabelText('User');

    expect(user).toBeTruthy();
  })

  it("Entrada de usuário não permitido", async ()=>{
     render(<UserProvider><Login/></UserProvider>); //reinderização de componente

     const user = screen.getByTestId('user-field');
     fireEvent.change(user, {target:{value: 'andre@123'}}) 

     const password = screen.getByTestId('password-field');
     fireEvent.change(password, {targe:{value: '123'}})

     const btn = await waitFor(()=> screen.getByTestId('form-btn') )

     fireEvent.click(btn);
  })
})