import React, {Fragment, ReactNode} from 'react';
import {CgMenuRound} from "react-icons/cg";
import {Box, IconButton} from '@mui/material';
import './table-menu.scss'
import MenuMulti from "../menu-multi/menu-multi";

const TableItemMenuMulti = ({children}) => {

    const [anchorEl, setAnchorEl] = React.useState(null);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <Fragment>
            <Box className='table-item-menu-multi'>
                <IconButton
                    onClick={handleClick}
                    sx={{ ml: 2 }}
                    aria-haspopup="true"
                    className='table-item-menu-multi__icon-button'
                >
                    <CgMenuRound size={30}/>
                </IconButton>
            </Box>
            <MenuMulti
                width='auto'
                anchorEl={anchorEl}
                onClose={handleClose}
                onClick={handleClose}
                color={'white'}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
                {children}
            </MenuMulti>
        </Fragment>
    );
}

export default TableItemMenuMulti;
