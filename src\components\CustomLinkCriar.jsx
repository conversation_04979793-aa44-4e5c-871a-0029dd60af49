import React, {useContext, useEffect, useState} from 'react';
import FormsNewFazenda from './FormsNewFazenda';
import { IoClose } from "react-icons/io5"

import { <PERSON><PERSON>, Modal, Card, NavDropdown } from 'react-bootstrap'
import FormsNewUser from './FormsNewUser';
import FormsNewViatura from '../rafatoring/pages/vehicals/FormsNewViatura';
import FormsNewProprietario from './FormsNewProprietario';

import {AiOutlinePlusCircle} from  'react-icons/ai'
import FormsNewArea from './FormsNewArea';


function CustomLinkCriar(props) {
  const[open, setOpen] = useState(false);
  const [fluxo, setFluxo] = useState();

  const handleTitle = () =>{
    switch(props.tipo){
      case 'policiais':
        return (
          'Criar novo Usuário'
        )
      case 'fazendas':
        return (
          'Criar nova Fazenda'
        )
      case 'viatura':
        return (
          'Criar nova Viatura'
        )
      case 'prop':
        return (
          'Criar novo Proprietário'
        )
      case 'areas':
        return (
          'Criar nova Área'
        )
      default:
        return false;
    }
  }

  const handleFluxo = () =>{
    switch(fluxo){
      case 'policiais':
        return (
          <FormsNewUser  closeModal={() => setOpen(false)}/>
        )
      case 'fazendas':
        return (
          <FormsNewFazenda closeModal={() => setOpen(false)}/>
        )
      case 'viatura':
        return (
          <FormsNewViatura/>
        )
      case 'prop':
        return (
          <FormsNewProprietario closeModal={() => setOpen(false)}/>
        )
      case 'areas':
        return (
          <FormsNewArea/>
        )
      default:
        return false;
    }
  }

  return (
    <>
        {props.tipo == 'fazendas'
            &&
            <>
                <NavDropdown.Item onClick={()=>{
                    setOpen(true)
                    setFluxo(props.tipo)
                }}>Adicionar Fazenda</NavDropdown.Item>
            </>
        }
        {props.tipo == 'prop'
            &&
            <>
                <NavDropdown.Item onClick={()=>{
                    setOpen(true)
                    setFluxo(props.tipo)
                }}>Adicionar Proprietário</NavDropdown.Item>
            </>
        }
        {props.tipo == 'viatura'
            &&
            <>
                <NavDropdown.Item onClick={()=>{
                    setOpen(true)
                    setFluxo(props.tipo)
                }}>Cadastrar Viaturas</NavDropdown.Item>
            </>
        }
        {props.tipo == 'policiais'
            &&
            <>
                <NavDropdown.Item onClick={()=>{
                    setOpen(true)
                    setFluxo(props.tipo)
                }}>Cadastrar Policiais</NavDropdown.Item>
            </>
        }
        {props.tipo == 'areas'
            &&
            <>
                <AiOutlinePlusCircle className='iconCriar'/>
                <p>Criar <br/>Nova Área</p>

            </>
        }
      
      <Modal
        show={open}
        onHide={() => setOpen(false)}
        aria-labelledby="example-modal-sizes-title-lg"
        contentClassName='dialogModal'
      >
        <Modal.Header>
          <Modal.Title>
            <h5 style={{fontWeight:'bolder'}}>{handleTitle()}</h5>
          </Modal.Title>
          <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>setOpen(false)} style={{fontSize:25, color:'#868686'}}/></Button>
        </Modal.Header>
        <Modal.Body>{handleFluxo()}</Modal.Body>
      </Modal>
    </>
  );
}

export default CustomLinkCriar;