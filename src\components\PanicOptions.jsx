import React, { useEffect, useState } from 'react'
import {Card, Row, Col, Button, Modal} from 'react-bootstrap'
import KeyboardArrowRightIcon from '@material-ui/icons/KeyboardArrowRight';
import { IoClose } from 'react-icons/io5';
import styled from 'styled-components'
import Mapa from './Mapa'
import axios from 'axios';
import { URL } from '../services';
import MapViatura from './MapViatura';
import { useHistory } from 'react-router';

const SearchInput = styled.input`
  background-image: url('https://cdn0.iconfinder.com/data/icons/very-basic-2-android-l-lollipop-icon-pack/24/search-512.png');
  background-size: 20px;
  background-repeat: no-repeat;
  background-position: right;
  text-indent: 10px;
  border-radius: 8px;
  padding: 10px;
  border: none;
  font-size: 16px;
  box-shadow: 0px 5px 7px rgb(130 131 132 / 15%);
  &:focus {
    outline: none;
  }
`;
const ViaturaCard = styled.div`
  box-shadow: 0px 5px 7px rgb(174 174 174 / 15%);
  border-radius: 20px;
  padding: 20px;
`;


function PanicOptions(props) {

  const [modalSendViaturaOpen, setModalSendViaturaOpen] = useState(false)
  const history = useHistory();

  const handleClickDetails = (id) => {
    // window.location.href = "/alertdetail/" + id
    history.push('/alertdetail/' + id)
  }

    return (
        <>
            <Card style={{backgroundColor:'white',width:'230px',padding:'5px',border:'none'}}>
                <Card.Header style={{backgroundColor:'white'}}>
                    Ações
                </Card.Header>
                <Card.Body>
                    <div style={{display:'flex',flexDirection:'column'}}>
                        <button onClick={() => handleClickDetails(props.data.ID)} style={{display:'flex',border:'None',backgroundColor:'white',color:'black',textAlign:'start'}}>
                            <KeyboardArrowRightIcon  /><p style={{marginLeft:'10px'}}>Detalhes do chamado</p>
                        </button>
                    </div>
                </Card.Body>
            </Card>

            {modalSendViaturaOpen && 
              <ModalSendViatura 
                open={modalSendViaturaOpen} 
                close={() => setModalSendViaturaOpen(false)} 
                data={props.data}
              />}
        </>
    )
}

function ModalSendViatura ({open, close, data}) {
  const [search, setSearch] = useState("")
  const [viatura, setViatura] = useState([])

  console.log(window.localStorage.getItem('@ViaturaProx'))

    useEffect(()=>{
      axios.post(URL+'buscar_viatura',{
        id_viat: window.localStorage.getItem('@ViaturaProx')
      })
      .then(res=>{
        setViatura(res.data)
      })
    },[])

    return(
        <Modal
          show={open}
          onHide={() => close()}
          aria-labelledby="example-modal-sizes-title-lg"
          contentClassName='dialogModal' 
        >
        
        <Modal.Header>
            <Modal.Title>
                <h5 style={{fontWeight:'bolder'}}>Procure a viatura que deseja enviar para visitar a fazenda desejada</h5>
            </Modal.Title>
            <Button variant='light' style={{backgroundColor:'#fff', border:'none'}}><IoClose onClick={()=>close()} style={{fontSize:25, color:'#868686'}}/></Button>
        </Modal.Header>

        <Row
          style={{padding: 20}}
        >
            <SearchInput 
              type="text"
              id="pesquisar_tabela_area"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Pesquisar..."
            />
        </Row>

        <Row>
          <p style={{textAlign: "center", fontSize: 14, color: "#0076C1"}}>Viatura sugerida</p>
        </Row>

        <Row 
          style={{padding: 40}}
        >
          <ViaturaCard>
            <Row>
              <Col>
                  <MapViatura
                    largura={"100%"}
                    height={"100%"}
                    idViatura={viatura.id_viat}
                    latitude={data.LATITUDE}
                    longitude={data.LONGITUDE}
                  />
              </Col>
              <Col>
                <Row>
                  <p style={{color: "#333", fontSize: 15, fontWeight: 700, margin: 0, marginBottom: 4}}>Viatura #{viatura.id_viat}</p>
                  <p style={{color: "#333", fontSize: 12, fontWeight: 500, margin:3 }}>Distância: <span style={{fontWeight: 200}}>{window.localStorage.getItem('@Distancia')}KM</span></p>
                  <p style={{color: "#333", fontSize: 12, fontWeight: 500, margin:3 }}>Modelo: <span style={{fontWeight: 200}}>{viatura.modelo_viat}</span></p>
                  <p style={{color: "#333", fontSize: 12, fontWeight: 500, margin:3 }}>Localização: <span style={{fontWeight: 200}}>exemplo</span></p>
                </Row>
                <Row style={{marginTop: 12}}>
                  <button 
                    onClick={()=> alert(`selecionaaar`)}
                    style={{
                      backgroundColor: "rgba(0, 118, 193, 1)",
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 8,
                      border: 'none',
                      borderRadius: 100,
                      color: '#fff',
                      fontWeight: 500,
                      fontSize: 14,
                      width: '80%'
                    }}
                  >
                    Selecionar
                  </button>
                </Row>
              </Col>
            </Row>
          </ViaturaCard>
        </Row>


        </Modal>
    )
}
export default PanicOptions

