import React,{useEffect,useState} from 'react'

function Timer(props) {

    const date1=props.data.split("T")[0]
    const time1=props.data.split("T")[1].split('.')[0]
    //console.log(time1)
    // const str=date1+" "+time1+" UTC"

    var date = new Date;
    date.setDate(date1.split("-")[2])
    date.setMonth(date1.split("-")[1])
    date.setYear(date1.split("-")[0])

    date.setHours(time1.split(":")[0])
    date.setMinutes(time1.split(":")[1])
    date.setSeconds(time1.split(":")[2])
    
    var date2=Date.now()


    const duration=Math.abs((date2-date))
    
    var seconds = Math.floor((duration / 1000) % 60)
    var minutes = Math.floor((duration / (1000 * 60)) % 60)
    var hours2 = Math.floor((duration / (1000 * 60 * 60)) % 24)

    const [sec, setSec] = useState(seconds)
    const [min, setMin] = useState(minutes)
    const [hour, setHour] = useState(hours2)

    console.log(date,date2)


    var milliseconds = Math.floor((duration % 1000) / 100)

    

    //console.log(hours2)
    const hell=(value)=>{
        if(value>9){
            return ""
        }
        return 0
    }

    useEffect(() => {
        setInterval(() => {
            setSec(sec=>sec+1)            
        }, 1000);
    }, [])

    useEffect(() => {
        if(sec>=60){
            setMin(min=>min+1)
            setSec(0)
        }
        if(min>=60){
            setSec(0)
            setMin(0)
            setHour(hour=>hour+1)
        }
    }, [sec])


    return (
        
            <div className="alert-tempo nome-fazenda">{hell(hour)+""+hour+":"+hell(min)+""+min+":"+""+hell(sec)+sec}</div>
        
    )
}

export default Timer
