import {render, screen} from "@testing-library/react";
import ColumnAreaAtivaInativa from '../components/customTable/columnsRenders/ColumnAreaAtivaInativa';

describe("Area Ativa-Inativa", ()=>{
  it("Area Ativa-Inaiva", () =>{

    const name = "nameTest";
    const column = "columnTest";

    const rowAtivo = {
      columnTest: 'ativo'  
    }
    const rowInativo = {
      columnTest: 'inativo'  
    }
    const index = 0;

    const result = ColumnAreaAtivaInativa(name, column);

    expect(result.name).toEqual(name); 
    expect(typeof result.cell).toEqual("function");

    render(result.cell(rowAtivo, index));
    const check1 = screen.getByText('ativo');
    expect(check1).toBeTruthy();

    render(result.cell(rowInativo, index));
    const check2 = screen.getByText('inativo');
    expect(check2).toBeTruthy();
  })
})