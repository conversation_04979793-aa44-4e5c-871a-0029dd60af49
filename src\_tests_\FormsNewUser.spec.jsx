import React from 'react';
import {render, screen} from "@testing-library/react";
import {UserProvider} from '../context/authContext';
import FormsNewUser from '../components/FormsNewUser';


describe("Testing FormNewUser", () => {
  it("show the FormsNewUser", () => {
    render(<UserProvider><FormsNewUser/></UserProvider>);

    const form = screen.getByText('Posto/Graduação');
    const name = screen.getByText('Nome');
    const unity = screen.getByText('Unidade');
    const email = screen.getByText('Email');
    const phone = screen.getByText('Telefone');
    const photograph = screen.getByText('Foto');
    const senha = screen.getByText('Senha');
  
    expect(form).toBeTruthy();
    expect(name).toBeTruthy();
    expect(unity).toBeTruthy();
    expect(email).toBeTruthy();
    expect(phone).toBeTruthy();
    expect(photograph).toBeTruthy();
    expect(senha).toBeTruthy();
  })

  it("Cadastro de Usuário", async ()=>{
     
      const screen = render(
        <UserProvider>
          <FormsNewUser
            row={{
              "matricula": "Policial",
              "senha": "123456",
              "posto_graduacao": "SP",
              "nome": "Aiba",
              "foto": "any",
              "unidade": "PC-SP",
              "email": "<EMAIL>",
              "telefone": "33415566",
              "coordenador": "Sim",
              "ativo_inativo": "Ativo"
            }}
          />
        </UserProvider>
      );

  expect(screen).toBeTruthy();
  
  })

})

