import React, { useState, useContext } from 'react';
import Swal from 'sweetalert2'
import { URL_API } from '../services';
import axios from 'axios';
import {useHistory} from 'react-router-dom';

const UserContext = React.createContext();

export function useUserContext() {
  return useContext(UserContext);
}

export function handleUserLogout() {
  Swal.fire({
    title:'Sair',
    text:'Deseja sair do sistema',
    icon:'question',
    showCancelButton: true,
    cancelButtonText: 'Não',
    confirmButtonText: `Sim`,
  }).then((result) => {
    if (result.isConfirmed) {
      window.localStorage.removeItem("@Nome")
      window.localStorage.removeItem("@Ident")
      window.location.href = "/"
    }
  })
}

export function UserProvider({ children }) {

  const [userId, setUserId] = useState();
  const [user, setUser] = useState({});
  // TO-DO setar valor padrão para ""
  const [userRole, setUserRole] = useState();
  const [count, setCount] = useState(1);
  const [bloqueado, setBloqueado] = useState(false);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);
  const [textHeader, setTextHeader] = useState();
  const [toggle, settoggle] = useState(0)
  const [select, setselect] = useState("default")
  const [search1, setsearch1] = useState("")


  const isAuthenticated = () => {
    return userId ? true : false;
  }

  const handleUserLogin = (username, password, token) => {
    setLoading(true)
    axios.post(URL_API+'autenticar',{
      usuario: username,
      senha: password,
      chave: token
    }).then(res=>{
      switch (res.data.message){
        case "INVALID":
          setCount(count+1);
          if(count > 2){
            //setError('Usuario bloqueado, reinicie o aplicativo e tente novamente');
            Swal.fire({title:'Erro', text:'Usuario bloqueado, reinicie o aplicativo e tente novamente'});
            setBloqueado(true);
            setLoading(false)
            break;
          }
          else{
            //setError('Username e/ou Senha incorreto(s). '+ (3-count) +' Tentativa(s) Restante(s).');
            Swal.fire({title:'Erro', text:'Username e/ou Senha incorreto(s). '+ (3-count) +' Tentativa(s) Restante(s).'});
            setLoading(false);
            break;
          }
        case "BLOCKED":
          Swal.fire({title:'Erro', text:'Usuario bloqueado, reinicie o aplicativo e tente novamente'});
          setLoading(false);
          break;
        case "EXCEEDED":
          Swal.fire({title:'Erro', text:'Usuario bloqueado, reinicie o aplicativo e tente novamente'});
          setLoading(false);
          break;
        case "EXPIRED":
          Swal.fire({title:'Erro', text:'Usuario bloqueado, reinicie o aplicativo e tente novamente'});
          setLoading(false);
          break;
        default:
          const dataUser = res.data.data_user
          // axios.post(URL_API+'sms',{
          //     number: res.data.data_user.TELEFONE,
          //     key: token
          // }).then(res=> {
          //   setShow(true);
          //   setLoading(false)
          //   console.log(res.data)
          // })
          // .catch(err => {
          //   console.log(err)
          // })
          setUser(res.data.data_user);
          setLoading(false);
          window.localStorage.setItem('@Ident', dataUser.IDENTIFICACAO)   
          window.localStorage.setItem('@Nome', dataUser.NOME)
          window.localStorage.setItem('@Id', dataUser.ID_USUARIO)
          window.location.href = "/home"
      }
    }).catch(error =>{
      console.log(error);
      Swal.fire({title:'Erro', text:'Erro no servidor'});
      console.log(error);
      setLoading(false);
    })
  }
  const handleCodigo = (codigo) =>{
    setLoading(true);
    const key = window.localStorage.getItem('@Token');
    console.log('chaveAuthProvider',key)
    axios.post(URL_API+'SMS_CHECKD',{
      code: codigo,
      key: key
    }).then(res=>{
      setLoading(false);
      if(res.data.message === 'OK'){
        // As indentificações são -> ADMINAIBA - COORDPM - PM
        console.log(user);
        window.localStorage.setItem('@Ident', user.IDENTIFICACAO)   
        window.localStorage.setItem('@Nome', user.NOME)
        window.localStorage.setItem('@Id', user.ID_USUARIO)
        window.location.href = "/home"    
      }
      else{
        Swal.fire({title:'Erro', text:'Reinicie o aplicativo e tente novamente'});
      }
    }).catch(res=>{
      Swal.fire({title:'Erro', text:'Erro no servidor'});
    })
  }

  return (
    <UserContext.Provider value={{
      select,
      setselect,
      search1,
      setsearch1,
      toggle,
      settoggle,
      userId, 
      handleUserLogin, 
      handleUserLogout, 
      isAuthenticated, 
      userRole, 
      setUserRole, 
      bloqueado, 
      show, 
      setShow, 
      handleCodigo,
      loading,
      textHeader,
      setTextHeader
    }}>
      {children}
    </UserContext.Provider>
  );
}