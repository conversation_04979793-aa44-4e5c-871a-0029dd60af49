import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';
import React from 'react'


function SelectStatus({status, setStatus}) {

    return (
        <FormControl fullWidth variant="standard">
            <Select
                value={status}
                onChange={(e) =>setStatus(e.target.value)}
            >
                <MenuItem value="Aguardando atendimento">Aguardando atendimento</MenuItem>
                <MenuItem value="Em atendimento">Em atendimento</MenuItem>
                <MenuItem value="Concluída">Concluída</MenuItem>
                <MenuItem value="Cancelada">Cancelada</MenuItem>
                <MenuItem value="">Todas ocorrências</MenuItem>
            </Select>
        </FormControl>
    )
}

export default SelectStatus;
