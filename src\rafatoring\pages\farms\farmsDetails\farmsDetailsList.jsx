import React, { useEffect, useState } from 'react';
import { TableComponent } from '../../../components/organisms';
import { TableHead, TableRow, TableCell, TableBody } from '@mui/material';
import FarmsDetailsSingle from './farmsDetailsSingle';

const FarmsDetailsList = ({visits}) => {

const [page, setPage] = useState(0);
const [rowsPerPage, setRowsPerPage] = useState(5);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

    return (  
        <>
            {visits
                ?
                <TableComponent 
                    count={visits.length} 
                    page={page} 
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    title='Listagem de Visitas na Fazenda'
                >
                    <TableHead>
                        <TableRow>
                            <TableCell>Data</TableCell>
                            <TableCell align='center'>Rota</TableCell>
                            <TableCell align='center'>Responsável</TableCell>
                            <TableCell align='center'>Viatura</TableCell>
                            <TableCell align='center'>Status</TableCell>
                            {/* <TableCell></TableCell> */}
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {visits.length <= 0
                          ?
                            <span style={{padding: '10px', fontWeight: 'bold'}}>Nenhuma visita registrada</span>
                          :
                            <FarmsDetailsSingle data={visits} page={page} rowsPerPage={rowsPerPage}/>
                        }
                    </TableBody>
                </TableComponent>
                :
                <h2>Carregando...</h2>
            }
        </>             
    ); 
}

export default FarmsDetailsList;



