import React, { useState, useContext, useEffect } from 'react';
import {SocketContext} from '../context/socketContext';
import Swal from 'sweetalert2'
import { useImmer } from 'use-immer';
import axios from 'axios';
import { URL, URL_SOCKET } from '../services';


const AlertContext = React.createContext();

export function useAlertContext() {
  return useContext(AlertContext);
}  

export function AlertProvider({ children }) {

    const [panico, setPanico] = useState(false);
    const socket = useContext(SocketContext);
    const [datas, setData] = useImmer([]);

    useEffect(() => {
        ////////////////////////////////PEGAR O ALERTAS ACIONADOS/////////////////////////////////////////////////
        axios.get(URL_SOCKET+'panico_ocorrencias_armada')
        .then(data => {
            
            for(let i=0; i < data.data.length; i++){
                if(data.data[i].IDENTIFICACAO != "FZD"){
                    setData(draft => { 
                        draft.push([data.data[i].NOME,'Polical',data.data[i].ID,'acionado',data.data[i].LATITUDE,data.data[i].LONGITUDE, data.data[i].HORA])
                    });
                }
                else{
                    setData(draft => { 
                        draft.push([data.data[i].NOME,data.data[i].ENDERECO,data.data[i].ID,'acionado',data.data[i].LATITUDE,data.data[i].LONGITUDE, data.data[i].HORA])
                    });
                }
            }
        }).catch(err => {
            console.error(err); 
        })
        ///////////////////////////////////////ALERTAS EM ANDAMENTO//////////////////////////////////////////////
        axios.get(URL_SOCKET+'panico_ocorrencias_andamento')
        .then(data => {
            
            for(let i=0; i < data.data.length; i++){
                if(data.data[i].IDENTIFICACAO != "FZD"){
                    setData(draft => { 
                        draft.push([data.data[i].NOME,'Policial',data.data[i].ID,'andamento',data.data[i].LATITUDE,data.data[i].LONGITUDE, data.data[i].HORA])
                    });
                }
                else{
                    setData(draft => { 
                        draft.push([data.data[i].NOME,data.data[i].ENDERECO,data.data[i].ID,'andamento',data.data[i].LATITUDE,data.data[i].LONGITUDE, data.data[i].HORA])
                    });
                }
            }
        }).catch(err => {
            console.error(err); 
        })
          
        socket.on('panico',(data)=>{
            console.log(data);
            setPanico(true);
            if(data.dados_quem_foi[0]['IDENTIFICACAO']){
                setData(draft => { 
                    draft.push([data.dados_quem_foi[0]['NOME'],'',data.last_id,'acionado', data.dados_panico.latitude, data.dados_panico.longitude, data.dados_panico.hora])
                });
            }
            else{
                setData(draft => { 
                    draft.push([data.dados_quem_foi[0][2],data.dados_quem_foi[0][6],data.last_id,'acionado', data.dados_panico.latitude, data.dados_panico.longitude, data.dados_panico.hora])
                });
            }
        });

        socket.on('Atenção ocorrencia cancelada', (res)=>{
            console.log('cancelado',res.ID);
            const index = datas.findIndex((data)=> data[2] === res.ID);
            setData(draft => {
                draft.splice(index,1)
            });
        })
        socket.on('Atenção viatura a caminho', (res)=>{
            console.log('andamento',res);
            // const index = datas.findIndex((data)=> data[2] === res.ID);
            // setData(draft => {
            //     draft.splice(index,1)
            // });
        })
         
    }, []);

    const handleEmAndamento = id =>{
        axios.post(URL_SOCKET+'panico_acaminho', {
            id:id,
            id_viatura: Number(window.localStorage.getItem('@ViaturaProx'))
          }).then(res => {
            Swal.fire(`Chamado em Andamento!`)

            const index = datas.findIndex((data)=> data[2] === id);

            setData(draft => {
                draft[index][3] = 'andamento'
            });

          }).catch(err => {
            console.error(err);
            Swal.fire(`Erro: ${err}`)
          })
    }    
    const handlePronto = id =>{
        axios.post(URL_SOCKET+'panico_finalizado', {
            id: id
          }).then(res => {
            Swal.fire(`Chamado Resolvido!`)

            const index = datas.findIndex((data)=> data[2] === id);

            setData(draft => {
                draft.splice(index,1)
            });

            console.log(res);
           
          }).catch(err => {
            console.error(err);
            Swal.fire(`Erro: ${err}`)
          })
    }    


  return (
    <AlertContext.Provider value={{datas, handlePronto, handleEmAndamento}}>
      {children}
    </AlertContext.Provider>
  );
}