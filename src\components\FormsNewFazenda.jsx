import React, {useContext, useEffect, useState} from 'react';
import Header from '../components/Header'
import Footer from '../components/Footer'

import axios from 'axios';

import { 
    Container,
    Row, 
    Col, 
    Card, 
    Form, 
    Button, 
    InputGroup,
    FormControl,
    Spinner,
} from 'react-bootstrap'
import { URL } from '../services';
import Swal from 'sweetalert2';
import { Typeahead } from 'react-bootstrap-typeahead'; // ES2015
import { useImmer } from 'use-immer';

function FormsNewFazenda({ closeModal }) {

    const [load, setLoad] = useState(false);
    const [nome, setNome] = useState();
    const [prop, setProp] = useState([]);
    const [municipio, setMunicipio] = useState();
    const [codigo, setCodigo] = useState();
    const [latitude, setLatitude] = useState();
    const [longitude,setLongitude] = useState();
    const [area, setArea] = useState([]);
    const [options, setOptions] = useImmer([]);
    const [status, setStatus] = useState('ATIVO')
    const [areasOp, setAreasOp] = useImmer([])
    const [errors, setErrors] = useState({});
    const [extras, setExtras] = useState('1');

    const now = new Date

    useEffect(()=>{
        axios.get(URL+'selecionar_todos_proprietarios')
        .then(res=>{
            const optionsOpRequest = res.data.map((item)=>({
                id:item.id_prop,
                name: item.nome_do_proprietario
            }))
            setOptions(optionsOpRequest)
        })
        .catch(err=>console.log(err))
    },[])
    useEffect(()=>{
        axios.get(URL+'todas_areas')
        .then(res=>{
                const itensAreas = res.data
                const optionsAreasRequest = itensAreas.map((item)=>({
                    id:item.id,
                    name: item.nome_da_area
                }))
                setAreasOp(optionsAreasRequest)
        })
        .catch(err=>console.log(err))
      },[])
    function submit(event){
        event.preventDefault();

        let hasError = false;
        let validations = {};

        if(!(Number.isFinite(Number(latitude)) && Number(latitude) >= -90 && (!isNaN(latitude)))){
            validations = { ...validations, latitude: true } 
            hasError = true;         
        }

        if(!(Number.isFinite(Number(longitude)) && Number(longitude)<= 90 && (!isNaN(longitude)))){
            validations = { ...validations, longitude: true }  
            hasError = true;         
        }

        if(prop.length == 0){
            validations = { ...validations, prop: true }  
            hasError = true;  
        }

        if(area.length == 0){
            validations = { ...validations, area: true }  
            hasError = true;  
        }
         
        setErrors({ ...errors, ...validations }) 
        if(hasError) return;


        setLoad(true);
        axios.post(URL+'salvar_fazenda',{
            nome_da_fazenda: nome,
            proprietario: prop.id,
            area_de_operacao: area.nome,
            id_area: area.id,
            municipio: municipio,
            latitude: latitude,
            longitude: longitude,
            codigo_da_fazenda: codigo,
            data_do_cadastro:now.getDate()+'/'+now.getMonth()+'/'+now.getFullYear(),
            hora_do_cadastro: now.getHours()+':'+now.getMinutes()+':'+now.getSeconds(),
            ativo_inativo : status,
            pernoite: extras === '2' || extras === '3' ? '1' : '0',
            combustivel:extras === '1' || extras === '3' ? '1' : '0'
        })
        .then(res=>{
            setLoad(false);          
            Swal.fire(nome,'cadastrado com sucesso!','success')
            closeModal();
        })

        .catch(err=>{
            console.log(err)
            setLoad(false)
        })
    }
  
  return (
    <>
            <Form className="" onSubmit={submit}>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Nome da Fazenda <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setNome(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Proprietário <font className='obr'>*</font></Form.Label>
                            <Typeahead
                                id="basic-typeahead-single"
                                labelKey="name"
                                onChange={selected=>{
                                    //setProp(selected.replace('"','').replace('[',''))
                                    setProp(selected[0])
                                    console.log(selected)
                                }}
                                options={options}
                                placeholder="Digite aqui"
                                className='customInput shadow'
                                required
                            />
                            {errors.prop && <p className="formError">Selecione um proprietário!</p>}                          
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Município <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e => setMunicipio(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Área de Atuação <font className='obr'>*</font></Form.Label>
                            <Typeahead
                                id="basic-typeahead-single"
                                labelKey={option => option.name}
                                onChange={selected=>{
                                    //setProp(selected.replace('"','').replace('[',''))
                                    setArea(selected[0])
                                    console.log(selected)
                                }}
                                options={areasOp}
                                placeholder="Digite aqui"
                                className='customInput shadow'
                                required
                            />
                            {errors.area && <p className="formError">Selecione uma área de atuação</p>}                          
                        </Form.Group>
                    </Col>
                </Row>
                <Row>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Latitude <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e=>{ setLatitude(e.target.value); setErrors({ ...errors, latitude: false })}} required/>
                            {errors.latitude && <p className="formError">Latitude inválida</p>}                          
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Longitude <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e=>{ setLongitude(e.target.value); setErrors({...errors, longitude: false})}} required/>
                            {errors.longitude && <p className="formError">Longitude inválida</p>} 
                        </Form.Group>
                    </Col>
                </Row>
                <Row className="g-2">
                    <Col md={6}>
                        <Form.Group className="mb-3" controlId="formBasicPassword" hasValidation>
                            <Form.Label className='customLabel'>Código da Fazenda <font className='obr'>*</font></Form.Label>
                            <Form.Control className='customInput shadow' type="text" placeholder="Digite aqui" onChange={e=>setCodigo(e.target.value)} required/>
                        </Form.Group>
                    </Col>
                    <Col md>
                        <Form.Group className="mb-3" controlId="formBasicEmail">
                            <Form.Label className='customLabel'>Status <font className='obr'>*</font></Form.Label>
                            <select  className='customInput shadow form-control' value={status} onChange={e=>setStatus(e.target.value)}>
                                <option selected value="ATIVO">ATIVO</option>
                                <option value="INATIVO">INATIVO</option>       
                            </select>
                        </Form.Group>
                    </Col>
                </Row>
                <Row>
                <Col md>
                        <Form.Group className="mb-3" controlId="formBasicEmail">
                            <Form.Label className='customLabel'>Extras <font className='obr'>*</font></Form.Label>
                            <select  className='customInput shadow form-control' value={extras} onChange={e=>setExtras(e.target.value)}>
                                <option selected ></option>
                                <option value="1">Combustivel</option>
                                <option value="2">Pernoite</option>
                                <option value="3" >Pernoite e combustivel</option>     
                            </select>
                        </Form.Group>
                    </Col>
                </Row>

                <div class="col text-center mt-3">
                    <Button className='blueColor btnCadastro' type="submit" size='lg'>
                        {load 
                            ? 
                            <Spinner animation="border" />
                            :
                            'Criar Fazenda'
                        }
                    </Button>
                </div>
            </Form>
    </>
  );
}

export default FormsNewFazenda;