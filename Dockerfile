# Build Stage - Ultra-minimal for low-resource servers
FROM node:16-alpine AS build

# Install only essential dependencies (no compilation tools)
RUN apk add --no-cache git

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Configure npm for minimal resource usage
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm config set audit false && \
    npm config set fund false && \
    npm config set optional false

# Set very conservative memory limits
ENV NODE_OPTIONS="--max-old-space-size=1536"
ENV CI=true
ENV GENERATE_SOURCEMAP=false

# Install all dependencies in one go with minimal flags
RUN npm install --legacy-peer-deps --no-audit --no-fund --no-optional --maxsockets=1

COPY . .

# Build with ultra-conservative memory settings and optimizations
ENV NODE_OPTIONS="--max-old-space-size=1536 --optimize-for-size"
ENV GENERATE_SOURCEMAP=false
ENV INLINE_RUNTIME_CHUNK=false
ENV BUILD_PATH=build
RUN npm run build
 
# Production Stage
FROM nginx:stable-alpine AS production

# Install wget for healthcheck
RUN apk add --no-cache wget

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application
COPY --from=build /app/build /usr/share/nginx/html

# Copy and setup healthcheck
COPY healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]