# Build Stage - Optimized for reduced CPU usage
FROM node:18-alpine AS build

# Build arguments for optimization
ARG BUILD_CONCURRENCY=1
ARG NPM_CONFIG_JOBS=1

# Install minimal build dependencies
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    libc6-compat

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Configure npm for reduced resource usage
RUN npm config set maxsockets 1 && \
    npm config set progress false && \
    npm config set audit false && \
    npm config set fund false

# Set Node.js options with reduced memory allocation
ENV NODE_OPTIONS="--openssl-legacy-provider --max-old-space-size=2048"
ENV JOBS=$BUILD_CONCURRENCY
ENV npm_config_jobs=$NPM_CONFIG_JOBS

# Install dependencies with resource constraints
RUN npm ci --legacy-peer-deps --only=production --no-audit --no-fund --maxsockets=1

# Install dev dependencies separately to avoid conflicts
RUN npm ci --legacy-peer-deps --only=development --no-audit --no-fund --maxsockets=1

COPY . .

# Build with limited resources
RUN npm run build
 
# Production Stage
FROM nginx:stable-alpine AS production

# Install wget for healthcheck
RUN apk add --no-cache wget

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application
COPY --from=build /app/build /usr/share/nginx/html

# Copy and setup healthcheck
COPY healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]