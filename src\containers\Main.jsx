import React, {useContext, useEffect} from 'react';
import axios from 'axios';

import {SocketContext} from '../context/socketContext';
import Header from '../components/Header'
import Footer from '../components/Footer'
import Alert from '../components/Alert'
import Geolocation from '../components/Geolocation'
import {usePositionContext} from '../context/locationContext';
import Map from '../components/Mapa';

import { Container,Row, Col, Card } from 'react-bootstrap'
import { PREV, URL_API } from '../services';
import CustomButton from '../components/CustomButton';
import { useState } from 'react';

function Main() {

  const socket = useContext(SocketContext);
  const {latitude, longitude}  =  usePositionContext();

  const[largura, setLargura] = useState('');
  const [areasAtuacao, setAreasAtuacao] = useState([])


  useEffect(()=>{
    socket.on('connection',(data)=>{
      console.log(data)
    })
    console.log(socket)
  },[socket])

  useEffect(()=>{
    async function fetchData(){
        const areasResponse = await axios.get(URL_API+"todas_areas")
        setAreasAtuacao(areasResponse.data)
    }
    fetchData();
  },[]
);

  // useEffect(() => {
  //   setLargura(document.getElementById('cardMap').clientWidth);
  // }, [largura])
  
  
  return (
    <>
      <Header />
      <Container className="mt-5">   
        <Row>
          <Col md={5}>
            {PREV == 'aiba' 
              ? 
              <Card className='cardMap shadow' id='cardMap'>
                <Map 
                  largura={largura}
                />
                <Row className='mx-auto menu'>
                  <Col>
                    <CustomButton tipo='rotas'/>
                  </Col>
                  <Col>
                    <CustomButton tipo='fazendas'/>
                  </Col>
                </Row>
              </Card>
              :
              <Map/>
            }
          </Col>
          {/* <Col md={7}>
            <Alert/>
          </Col> */}
        </Row>
      </Container>

      <Footer />
    </>
  );
}

export default Main;