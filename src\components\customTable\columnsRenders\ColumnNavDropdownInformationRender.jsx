import React, {useState, useEffect} from 'react';
import './ColumnTitleDescriptionRender.css';
import { NavDropdown } from 'react-bootstrap';
import { IoClose } from "react-icons/io5"
import ReorderIcon from '@material-ui/icons/Reorder';
import ArrowForwardIosIcon from '@material-ui/icons/ArrowForwardIos';
import { useHistory } from 'react-router';
import ModalRedirecionarOcorrencia from '../../ModalRedirecionarOcorrencia';
import ModalCancelarOcorrencia from '../../ModalCancelarOcorrencia';
import { URL_API } from '../../../services';
import axios from 'axios'
import Swal from 'sweetalert2'

const ColumnNavDropdownInformationRender = (id, fetchOcorrencias) => {

  const history = useHistory();

  const handleClick = () => {

  }

  return {
    id: id,
    cell: (row) => {
      return (
        <div>
          <NavDropdown title={<ReorderIcon color="disabled" />} id="basic-nav-dropdown">
            <NavDropdown.Item >Ações</NavDropdown.Item>
            <NavDropdown.Divider />
            <NavDropdown.Item onClick={() => handleClick()}>{<ArrowForwardIosIcon />}Detalhes da Informação</NavDropdown.Item>
          </NavDropdown>
          
        </div>
      )
    }
  }
}

export default ColumnNavDropdownInformationRender; 