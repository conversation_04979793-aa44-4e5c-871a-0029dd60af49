import * as React from 'react'
import { render} from '@testing-library/react';
import CardRotas from '../components/CardRotas';
import ReactTooltip from 'react-tooltip';
import WhatshotIcon from '@material-ui/icons/Whatshot';
import HotelIcon from '@material-ui/icons/Hotel';


describe('Verificando  O CardRotas', () => {
  it('Verificando o CardRotas', () => {
    const screen = render(<CardRotas/>);

    expect(screen).toBeTruthy();

    const tooltip = render(<ReactTooltip/>)

    expect(tooltip).toBeTruthy();

    const whatshotIcon = render(<WhatshotIcon/>)

    expect(whatshotIcon).toBeTruthy();

    const hotelIcon = render(<HotelIcon/>)

    expect(hotelIcon).toBeTruthy();
  })
})