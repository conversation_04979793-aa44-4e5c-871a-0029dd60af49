import React from 'react'
import { SocketContext, socket } from './context/socketContext';
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom'
import PrivateRoute from './PrivateRoute'
import NotFound from './components/NotFound'
import Main from './containers/Main'
import Login from './components/Login'

import { UserProvider } from './context/authContext';
import { AlertProvider } from './context/alertContext';
import { PositionProvider } from './context/locationContext';
import Routes from './rafatoring/pages/routes';
import Fazendas from './rafatoring/pages/farms';
import DetalheFazendas from './rafatoring/pages/farms/farmsDetails';
import Areas from './rafatoring/pages/areas';
import Cadastros from './containers/Cadastros';
import DetalheRotas from './containers/DetalheRotas';
import AlertDetail from './rafatoring/pages/occurrences/called/AlertDetail';
import 'react-bootstrap-range-slider/dist/react-bootstrap-range-slider.css';
import { MapProvider } from './context/mapContext';
import PropertyCadastraods from './containers/PropertyCadastraods';
import RegisteredUsers from './rafatoring/pages/cops';
import Test from './containers/Test';
import Alert from './rafatoring/pages/occurrences/called';
import PoliceRoutes from './containers/PoliceRoutes';
import PoliceRoutes2 from './containers/PoliceRoutes2'
import Vehicals from './rafatoring/pages/vehicals';
import RegisteredRoutes from './rafatoring/pages/routes';
import InitialRoute from './containers/InitialRoute';
import InitialRoute2 from './containers/InitialRoute2';
import SingleListItem from './components/InitialRoutes/SingleListItem';
import RoutesList from './rafatoring/pages/routes/RoutesList';
import Ocorrencias from './rafatoring/pages/occurrences/recordedOccurrences/index.jsx';
import Terms from './containers/Terms.jsx';
import Information from './rafatoring/pages/occurrences/informationListing';
import Reports from './containers/Reports';
import Called from './rafatoring/pages/occurrences/called';

function App() {
  return (
    <SocketContext.Provider value={socket}>
      <AlertProvider>
        <UserProvider>
          <PositionProvider>
            <MapProvider>
              <Router>
                <Switch>
                  <Route exact path="/" component={Login} />
                  <Route exact path="/terms" component={Terms} />
                  <PrivateRoute exact path="/home" component={Main} />
                  <PrivateRoute exact path='/fazendas' component={Fazendas} />
                  <PrivateRoute exact path='/detalhefazenda/:id' component={DetalheFazendas} />
                  <PrivateRoute exact path='/detalherota/:id' component={DetalheRotas} />
                  <PrivateRoute exact path='/areas' component={Areas} />
                  <PrivateRoute exact path='/cadastros' component={Cadastros} />
                  <PrivateRoute exact path='/property' component={PropertyCadastraods} />
                  <PrivateRoute exact path='/registeredUsers' component={Called} />
                  <PrivateRoute exact path='/alert' component={Alert} />
                  <PrivateRoute exact path='/test' component={SingleListItem} />
                  <PrivateRoute exact path='/alertdetail/:id' component={AlertDetail} />
                  <PrivateRoute exact path='/InitialRoute2/:areaId/:rotaId/:responsavelId/:dataInicio' component={InitialRoute2} />
                  <PrivateRoute exact path='/property' component={PropertyCadastraods} />
                  <PrivateRoute exact path='/policiais' component={RegisteredUsers} />
                  <PrivateRoute exact path='/PoliceRoutes' component={PoliceRoutes} />
                  <PrivateRoute exact path='/PoliceRoutes2' component={PoliceRoutes2} />
                  <PrivateRoute exact path='/rotas' component={Routes} />
                  <PrivateRoute exact path='/InitialRoute/:areaId/:rotaId' component={InitialRoute} />
                  <PrivateRoute exact path='/viaturas' component={Vehicals} /> 
                  <PrivateRoute exact path='/ocorrencias' component={Ocorrencias} />                
                  <PrivateRoute exact path='/information' component={Information} />                
                  <PrivateRoute exact path='/reports' component={Reports} />                
                  <PrivateRoute component={NotFound} />
                </Switch>
              </Router>
            </MapProvider>
          </PositionProvider>
        </UserProvider>
      </AlertProvider>
    </SocketContext.Provider>
  );
}

export default App